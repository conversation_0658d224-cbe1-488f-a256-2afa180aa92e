const e={layouts:{"Layout configuration":"Layout configuration","Layout mode":"Layout mode",default:"Default",classic:"Classic","Single column":"Single Column","overall situation":"Global","Background page switching animation":"Background page switching animation","Please select an animation name":"Please select an animation name",sidebar:"Sidebar","Side menu bar background color":"Background color of the side menu bar","Side menu text color":"Side menu text color","Side menu active item background color":"Background color of the side menu activation item","Side menu active item text color":"Side menu activation item text color","Show side menu top bar (logo bar)":"Display the top bar of the side menu (Logo Bar)","Side menu top bar background color":"Background color of the top bar of the side menu ","Side menu width (when expanded)":"Width of the side menu (Unfolding)","Side menu default icon":"Side menu default icon","Side menu horizontal collapse":"Side menu collapsed horizontally","Side menu accordion":"Side menu accordion","Top bar":"Top bar","Top bar background color":"Top bar background color","Top bar text color":"Top bar text color","Background color when hovering over the top bar":"Top bar hover background color","Top bar menu active item background color":"Background color of the top bar activation item","Top bar menu active item text color":"Top bar menu activation item text color","Are you sure you want to restore all configurations to the default values?":"Are you sure to restore all configurations to the default values?","Restore default":"Restore default","personal data":"Personal data",cancellation:"Cancellation","Dark mode":"Dark mode","Exit full screen":"Exit Full Screen","Full screen is not supported":"Your browser does not support full screen, please change another browser and try again~"},terminal:{Terminal:"Terminal","Command run log":"Command Run Log","No mission yet":"There is no task yet","Test command":"Test command","Install dependent packages":"Install dependent packages",Republish:"Republish","Clean up task list":"Clean up the task list",unknown:"Unknown","Waiting for execution":"Waiting for execution",Connecting:"Connecting",Executing:"Executing","Successful execution":"Executed successfully","Execution failed":"Failed to execute","Unknown execution result":"Execution result is unknown","Are you sure you want to republish?":"Are you sure to republish?","Failure to execute this command will block the execution of the queue":"Failed to execute this command will block queue execution.","Package manager":"Package manager","Please select package manager":"Please select a package manager","Switch package manager title":"Readonly WEB terminal, it can more conveniently execute npm install, npm build and other commands after CRUD and other operations. Please select an installed or your favourite NPM package manager below.","I want to execute the command manually":"I want to execute the command manually","Do not refresh the browser":"Do not refresh your browser.","Terminal settings":"Terminal setup","Install service port":"Install service port","The port number to start the installation service (this port needs to be opened for external network access)":"Start the port number of the installation service (External access requires to opened the port for public.)","Installation service startup command":"install service start command","Please execute this command to start the service (add Su under Linux)":"Please execute this command to start the service (add su under Linux)","Installation service URL":"Installation service URL","Please access the site through the installation service URL (except in debug mode)":"Please access the site through the installation service URL (except in debug mode)","Clean up successful tasks when starting a new task":"Please clean up successful tasks when starting a new task.","Back to terminal":"Back to terminal",or:"or","Site domain name":"Site domain name","The current terminal is not running under the installation service, and some commands may not be executed":"The current terminal is not running under the installation service, and some commands may not be executed.","Newly added tasks will never start because they are blocked by failed tasks":"Newly added tasks will never start because they are blocked by failed tasks!(Web terminal)"}};export{e as default};
