{"name": "build-admin", "version": "2.0.2", "license": "Apache-2.0", "scripts": {"dev": "vite --force", "build": "vite build && esno ./src/utils/build.ts", "lint": "eslint --ext .js,.jsx,.ts,.vue src", "lint-fix": "eslint --ext .js,.jsx,.ts,.vue src --fix", "format": "npx prettier --write ."}, "dependencies": {"@element-plus/icons-vue": "2.1.0", "@vueuse/core": "10.2.0", "axios": "1.4.0", "countup.js": "2.6.2", "echarts": "5.4.2", "element-plus": "2.3.7", "esno": "0.16.3", "font-awesome": "4.7.0", "lodash-es": "4.17.21", "mitt": "3.0.0", "nprogress": "0.2.0", "pinia": "2.1.4", "pinia-plugin-persistedstate": "3.1.0", "screenfull": "6.0.2", "sortablejs": "1.15.0", "v-code-diff": "1.5.1", "vue": "3.3.4", "vue-i18n": "9.2.2", "vue-qr": "4.0.9", "vue-router": "4.2.2"}, "devDependencies": {"@types/lodash-es": "4.17.7", "@types/node": "20.3.1", "@types/nprogress": "0.2.0", "@types/sortablejs": "1.15.1", "@typescript-eslint/eslint-plugin": "5.60.0", "@typescript-eslint/parser": "5.60.0", "@vitejs/plugin-vue": "4.2.3", "async-validator": "4.2.5", "dotenv": "16.3.1", "eslint": "8.43.0", "eslint-config-prettier": "8.8.0", "eslint-plugin-vue": "9.15.1", "prettier": "2.8.8", "sass": "1.63.6", "typescript": "5.1.3", "vite": "4.3.9", "vue-eslint-parser": "9.3.1"}}