<?php

namespace app\common\listen;

use app\common\logic\TrcLogic;
use app\common\logic\ErcLogic;
use app\common\logic\BscLogic;
use app\common\logic\OkcLogic;
use app\common\model\Wallet;
use think\console\Command;
use think\console\Input;
use think\console\input\Argument;
use think\console\input\Option;
use think\console\Output;
use think\facade\Cache;
use Tron\Support\Key as SupportKey;
use Workerman\Crontab\Crontab;
use Workerman\Worker;

class ChainsListen extends Command
{
    private $config = [
        'daemonize'     => false,
        'processTitle'  => 'ChainsListen',
    ];

    private $listens = ['trc' => TrcLogic::class, 'erc' => ErcLogic::class, 'bsc' => BscLogic::class, 'okc' => OkcLogic::class];

    private $details = [
        'trc' => [
            'address' => [
                'usdt' => '******************************************'
            ],
            'domain' => 'https://tronscan.org/#/address/',
            'symbol' => 'TRX',

        ],
        'erc' => [
            'address' => [
                'usdt' => '******************************************'
            ],
            'domain' => 'https://cn.etherscan.com/address/',
            'symbol' => 'ETH',
        ],
        'bsc' => [
            'address' => [
                'usdt' => '******************************************'
            ],
            'domain' => 'https://bscscan.com/address/',
            'symbol' => 'BNB'
        ],
        'okc' => [
            'address' => [
                'usdt' => 'TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t'
            ],
            'domain' => 'https://www.oklink.com/zh-cn/okc/address/',
            'symbol' => 'OKT'
        ],

    ];


    protected function configure()
    {
        $this->setName('chains:listen')
        ->addArgument('action', Argument::OPTIONAL, "start|stop|restart|reload|statuss", 'start')
        ->addOption('daemon', 'd', Option::VALUE_NONE, 'Run the workerman server in daemon mode.')
        ->setDescription('the chains listen command');
    }

    public function execute(Input $input, Output $output)
    {
        $action = $input->getArgument('action');

        if (DIRECTORY_SEPARATOR !== '\\') {
            if (!in_array($action, ['start', 'stop', 'reload', 'restart', 'status'])) {
                $output->writeln("<error>Invalid argument action:{$action}, Expected start|stop|restart|reload|status .</error>");
                return false;
            }
            global $argv;
            array_shift($argv);
            array_shift($argv);
            array_unshift($argv, 'think', $action);
        } elseif ('start' != $action) {
            $output->writeln("<error>Not Support action:{$action} on Windows.</error>");
            return false;
        }

        if ($input->hasOption('daemon') && $input->getOption('daemon')) {
            $this->config['daemonize'] = true;
        }
        $this->config['pidFile'] = make_dir(app()->getRuntimePath() ."worker/log/chains.pid");
        $this->config['logFile'] = make_dir(app()->getRuntimePath() ."worker/log/chains.log");

        if ('start' == $action) {
            $output->writeln('Starting chains listen ...');
        }

        $worker = new Worker();
        $worker->count = 5;
        $worker->onWorkerStart = function ($worker) {
            foreach ($this->listens as $contractType => $listen) {
                $this->cache("BlockNumber_{$contractType}", $listen::getBlockNumber());
            }
            $this->refreshAddress();
            if ($worker->id == 0) {
                new Crontab('1 * * * * *', function(){
                    $this->refreshAddress();
                });
            }
            if ($worker->id == 1) {
                new Crontab('0/5 * * * * *', function(){
                    $this->sweepBlock('trc');
                });
            }
            if ($worker->id == 2) {
                new Crontab('0/15 * * * * *', function(){
                    $this->sweepBlock('erc');
                });
            }
            if ($worker->id == 3) {
                new Crontab('0/5 * * * * *', function(){
                    $this->sweepBlock('bsc');
                });
            }
            if ($worker->id == 4) {
                new Crontab('0/5 * * * * *', function(){
                    $this->sweepBlock('okc');
                });
            }
        };
        
        foreach ($this->config as $key => $val) {
            Worker::${$key} = $val;
        }
        if (DIRECTORY_SEPARATOR == '\\') {
            $output->writeln('You can exit with <info>`CTRL-C`</info>');
        }
        Worker::runAll();
    }

    private function cache($key, $value = null)
    {
        if (is_null($value)) {
            return Cache::get($key);
        } else {
            return Cache::set($key, $value);
        }
    }
    private function sweepBlock($contractType)
    {
        try {
            $listen = $this->listens[$contractType] ?? false;
            if (!$listen) return;
            $newBlockNumber = $listen::getBlockNumber();
            if (!isset($newBlockNumber)) return;
            $oldBlockNumber = $this->cache("BlockNumber_{$contractType}");
            $this->cache("BlockNumber_{$contractType}", $newBlockNumber);
            $differ = $newBlockNumber - $oldBlockNumber;
            write_log("{$contractType} => 当前高度：{$oldBlockNumber},最新高度：{$newBlockNumber},差异高度：{$differ}", 'sweepBlock', $this->config['logFile']);
            for ($i = $oldBlockNumber; $i < $newBlockNumber; $i++) {
                $blockDatas = $listen::getBlockByNumber($i);
                if ($contractType == 'trc' && isset($blockDatas->blockID)) {
                    $this->checkTrcChainBlockDatas($contractType, $blockDatas);
                } elseif (isset($blockDatas->number)) {
                    $this->checkErcChainBlockDatas($contractType, $blockDatas);
                } else {
                    $i--;
                }
            }
        } catch (\Throwable $th) {
            write_log($th->getMessage(), 'sweepBlock', $this->config['logFile']);
        }
    }

    private function checkTrcChainBlockDatas ($contractType, $blockDatas)
    {
        try {
            $methods = [
                'a9059cbb' => 'transfer',
                '23b872dd' => 'transferFrom',
                '095ea7b3' => 'approve',
                'd73dd623' => 'increaseApproval',
            ];
            $address = $this->details[$contractType]['address'] ?? null;
            $symbol  = $this->details[$contractType]['symbol']  ?? null;
            if ($methods == null || $address == null || $symbol == null) return;
            foreach ($blockDatas->transactions as $blockData) {
                $success = $blockData['ret'][0]['contractRet'] ?? '';
                if ($success != 'SUCCESS') continue;
                
                $contract        = $blockData['raw_data']['contract'][0];
                $parameter       = $contract['parameter']['value'];
                $contractAddress = $parameter['contract_address'] ?? '';
            
                if (!$contractAddress) {
                    if ($contract['type'] != 'TransferContract') continue;
                    $txFrom = SupportKey::getBase58CheckAddress($parameter['owner_address']);
                    $txTo   = SupportKey::getBase58CheckAddress($parameter['to_address']);
                    $amount = bcdiv($parameter['amount'],1000000,2);
                    if ($amount <= 0) continue;
                    $this->checkAddress($contractType, $txTo, '转入', $amount, $symbol);
                    $this->checkAddress($contractType, $txFrom, '转出', $amount, $symbol);
                } else {
                    $contractAddress = SupportKey::getBase58CheckAddress($contractAddress);
                    $contract    = $parameter['data'];
                    $method          = substr($contract, 0, 8);
                    $amount          = hexdec(substr($contract, -64)) / bcpow('10', '6');
                    if (!in_array($contractAddress,$address) || !isset($methods[$method]) || $amount <= 0) continue;

                    switch ($methods[$method]) {
                        case 'transfer':
                            $txFrom   = SupportKey::getBase58CheckAddress($parameter['owner_address']);
                            $txTo     = SupportKey::getBase58CheckAddress(41 . substr($contract, -104, 40));
                            break;
            
                        case 'transferFrom':
                            $txFrom   = SupportKey::getBase58CheckAddress(41 . substr($contract, -168, 40));
                            $txTo     = SupportKey::getBase58CheckAddress(41 . substr($contract, -104, 40));
                            break;
            
                        default:
                            continue 2;
                    }
                    if ($amount <= 0) continue;
                    $this->checkAddress($contractType, $txTo, '转入', $amount, 'USDT');
                    $this->checkAddress($contractType, $txFrom, '转出', $amount, 'USDT');
                }
            }
        } catch (\Throwable $th) {
            write_log($th->getMessage(), 'checkData', $this->config['logFile']);
        }
    }
    private function checkErcChainBlockDatas ($contractType, $blockDatas)
    {
        try {
            $methods = [
                '0xa9059cbb' => 'transfer',
                '0x23b872dd' => 'transferFrom',
                '0x095ea7b3' => 'approve'
            ];
            $address = $this->details[$contractType]['address'] ?? null;
            $symbol  = $this->details[$contractType]['symbol']  ?? null;
            if ($methods == null || $address == null || $symbol == null) return;
            foreach ($blockDatas->transactions as $blockData) {
                if ($blockData->input == '0x') {
                    $txFrom = $blockData->from;
                    $txTo   = $blockData->to;
                    $amount = hexdec($blockData->value) / bcpow('10', '18');
                    if ($amount <= 0) continue;
                    $this->checkAddress($contractType, $txTo, '转入', $amount, $symbol);
                    $this->checkAddress($contractType, $txFrom, '转出', $amount, $symbol);
                } else {
                    $method = substr($blockData->input, 0, 10);
                    if (!in_array($blockData->to,$address) || !isset($methods[$method])) continue;
                    switch ($methods[$method]) {
                        case 'transfer':
                            $txFrom = $blockData->from;
                            $txTo   = '0x' . substr($blockData->input, 34, 40);
                            $amount = hexdec(substr($blockData->input, 74, 64)) / bcpow('10', '6');
                            break;
            
                        case 'transferFrom':
                            $txFrom = '0x' . substr($blockData->input, 34, 40);
                            $txTo   = '0x' . substr($blockData->input, 74, 40);
                            $amount = hexdec(substr($blockData->input, 138, 64)) / bcpow('10', '6');
                            break;
            
                        default:
                            continue 2;
                    }
            
                    if ($amount <= 0) continue;
                    $this->checkAddress($contractType, $txTo, '转入', $amount, 'USDT');
                    $this->checkAddress($contractType, $txFrom, '转出', $amount, 'USDT');
                }
            }
        } catch (\Throwable $th) {
            write_log($th->getMessage(), 'checkData', $this->config['logFile']);
        }
    }

    private function checkAddress($contractType, $address, $transfer, $amount, $symbol)
    {
        
        $Wallets = Cache::get("Wallets_{$contractType}") ?: [];
        $notice = false;
        $object = null;
        if (is_array($Wallets) && in_array($address,$Wallets)){
            $notice = true;
            $listen = '鱼苗监听';
            $object = Wallet::where('address',$address)->where('contract_type',$contractType)->find();
        }
        if ($notice && $object) {
            $chain = strtoupper($contractType);
            $object->updateBalance();
            $balance = $symbol == 'USDT' ? $object->usdt_balance : $object->main_balance;
            $domain  = $this->details[$contractType]['domain'] ?? '';
            $message = "<b>【{$listen}=>{$chain}{$transfer}】</b>"
                     . "<a href=\"{$domain}{$address}\">链上查询</a>\n"
                     . "<b>地址</b>：<code>{$address}</code>\n"
                     . "<b>金额</b>：{$amount} {$symbol}\n"
                     . "<b>余额</b>：{$balance} {$symbol}\n";
            queue_push('telegramNotice', ['message' => $message, 'mode' => 'HTML']);
        }
    }

    private function refreshAddress()
    {
        foreach ($this->listens as $contractType => $listen) {
            $wallets = Wallet::where('contract_type',$contractType)->column('address');
            $wallets && Cache::set("Wallets_{$contractType}", $wallets);
        }
    }
}