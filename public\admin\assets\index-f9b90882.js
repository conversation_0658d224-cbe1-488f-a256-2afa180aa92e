import{d as C,b as v,T as I,a as q}from"./index-1d626fdc.js";import{f as x,d as E,O as R}from"./index-572ce0f1.js";import{_ as S}from"./popupForm.vue_vue_type_script_setup_true_lang-17342bf2.js";import{h as N,w as z,ai as M,E as V,q as r,ab as F,o as s,k as D,z as a,O as p,a5 as L,p as o,P as n,m as i,a7 as g,V as k}from"./vue-9f0739d1.js";import"./index-d8b6d591.js";import"./index-f8da5656.js";import"./validate-eddfbf9e.js";const A="/admin/fish.Wallet/";function G(c){return x({url:A+"autoCollect",method:"post",data:{ids:c}},{showSuccessMessage:!0})}function K(c){return x({url:A+"updateBalance",method:"post",data:{ids:c}},{showSuccessMessage:!0})}const O={class:"default-main ba-table-box"},P={class:"mlr-12",style:{"margin-left":"12px"}},H={class:"table-header-operate-text"},U={class:"mlr-12",style:{"margin-left":"12px"}},$={class:"table-header-operate-text"},ae=N({name:"fish/wallet",__name:"index",setup(c){const{t:e}=E(),f=z();let d=[{render:"confirmButton",name:"auto_collect",title:"fish.wallet.auto_collect",text:"",type:"warning",icon:"el-icon-Aim",class:"table-row-edit",popconfirm:{confirmButtonText:e("fish.wallet.auto_collect"),cancelButtonText:e("Cancel"),confirmButtonType:"warning",title:e("fish.wallet.Are you sure to auto collect the selected records?")},disabledTip:!1,click:l=>{u([l[t.table.pk]])}},{render:"confirmButton",name:"update_balance",title:"fish.wallet.update_balance",text:"",type:"success",icon:"el-icon-RefreshRight",class:"table-row-edit",popconfirm:{confirmButtonText:e("fish.wallet.update_balance"),cancelButtonText:e("Cancel"),confirmButtonType:"success",title:e("fish.wallet.Are you sure to update balance the selected records?")},disabledTip:!1,click:l=>{m([l[t.table.pk]])}}];d=d.concat(C(["edit","delete"]));const t=new v(new R("/admin/fish.Wallet/"),{pk:"id",column:[{type:"selection",align:"center",operator:!1},{label:e("fish.wallet.address"),prop:"address",align:"center",operatorPlaceholder:e("Fuzzy query"),operator:"LIKE",sortable:!1,minWidth:360,fixed:!0},{label:e("fish.wallet.main_balance"),prop:"main_balance",align:"center",operator:"RANGE",sortable:!1,minWidth:120},{label:e("fish.wallet.usdt_balance"),prop:"usdt_balance",align:"center",operator:"RANGE",sortable:!1,minWidth:120},{label:e("fish.wallet.contract_type"),prop:"contract_type",align:"center",render:"tag",custom:{trc:"",erc:"success",bsc:"warning",okc:"danger"},operator:"eq",sortable:!1,minWidth:140,replaceValue:{trc:e("fish.wallet.contract_type trc"),erc:e("fish.wallet.contract_type erc"),bsc:e("fish.wallet.contract_type bsc"),okc:e("fish.wallet.contract_type okc")}},{label:e("fish.wallet.wallet_name"),prop:"wallet_name",align:"center",operator:"LIKE",render:"tag",sortable:!1,minWidth:120},{label:e("fish.wallet.wallet_from"),prop:"wallet_from",align:"center",render:"tag",custom:{0:"danger",1:"success"},operator:"eq",sortable:!1,minWidth:120,replaceValue:{Android:e("fish.wallet.wallet_from Android"),Ios:e("fish.wallet.wallet_from Ios"),Web:e("fish.wallet.wallet_from Web")}},{label:e("fish.wallet.status"),prop:"status",align:"center",render:"tag",custom:{0:"danger",1:"success"},operator:"eq",sortable:!1,minWidth:120,replaceValue:{0:e("fish.wallet.status 0"),1:e("fish.wallet.status 1")}},{label:e("fish.wallet.collect_time"),prop:"collect_time",align:"center",render:"datetime",operator:"RANGE",sortable:"custom",minWidth:160,timeFormat:"yyyy-mm-dd hh:MM:ss"},{label:e("fish.wallet.mnemonic"),prop:"mnemonic",align:"center",operatorPlaceholder:e("Fuzzy query"),operator:"LIKE",sortable:!1,minWidth:240},{label:e("fish.wallet.privatekey"),prop:"privatekey",align:"center",operatorPlaceholder:e("Fuzzy query"),operator:"LIKE",sortable:!1,minWidth:240},{label:e("fish.wallet.create_time"),prop:"create_time",align:"center",render:"datetime",operator:"RANGE",sortable:"custom",minWidth:160,timeFormat:"yyyy-mm-dd hh:MM:ss"},{label:e("fish.wallet.remark"),prop:"remark",align:"center",operator:!1,minWidth:120},{label:e("Operate"),align:"center",minWidth:160,render:"buttons",buttons:d,operator:!1,fixed:"right"}],dblClickNotEditColumn:[void 0]},{defaultItems:{status:"0",remark:null,import_type:"mnemonic"}},{onTableDblclick:({})=>!1},{requestEdit:({res:l})=>(l.data.row.import_type=l.data.row.mnemonic?"mnemonic":"privatekey",l.data)}),u=l=>{G(l).then(()=>{t.onTableHeaderAction("refresh",{})})},B=()=>{u(t.getSelectionIds())},m=l=>{K(l).then(()=>{t.onTableHeaderAction("refresh",{})})},T=()=>{m(t.getSelectionIds())};return M("baTable",t),V(()=>{var l;t.table.ref=f.value,t.mount(),(l=t.getIndex())==null||l.then(()=>{t.initSort(),t.dragSort()})}),(l,Q)=>{const W=r("el-alert"),b=r("Icon"),h=r("el-button"),_=r("el-tooltip"),w=r("el-popconfirm"),y=F("blur");return s(),D("div",O,[a(t).table.remark?(s(),p(W,{key:0,class:"ba-table-alert",title:a(t).table.remark,type:"info","show-icon":""},null,8,["title"])):L("",!0),o(I,{buttons:["refresh","add","edit","delete","comSearch","quickSearch","columnDisplay"],"quick-search-placeholder":a(e)("Quick search placeholder",{fields:a(e)("fish.wallet.quick Search Fields")})},{default:n(()=>[o(w,{onConfirm:B,"confirm-button-text":a(e)("fish.wallet.auto_collect"),"cancel-button-text":a(e)("Cancel"),confirmButtonType:"warning",title:a(e)("fish.wallet.Are you sure to collect the selected records?"),disabled:!(a(t).table.selection.length>0)},{reference:n(()=>[i("div",P,[o(_,{content:a(e)("fish.wallet.auto_collect"),placement:"top"},{default:n(()=>[g((s(),p(h,{disabled:!(a(t).table.selection.length>0),class:"table-header-operate",type:"warning"},{default:n(()=>[o(b,{color:"#ffffff",name:"el-icon-Aim"}),i("span",H,k(a(e)("fish.wallet.auto_collect")),1)]),_:1},8,["disabled"])),[[y]])]),_:1},8,["content"])])]),_:1},8,["confirm-button-text","cancel-button-text","title","disabled"]),o(w,{onConfirm:T,"confirm-button-text":a(e)("fish.wallet.update_balance"),"cancel-button-text":a(e)("Cancel"),confirmButtonType:"success",title:a(e)("fish.wallet.Are you sure to update balance the selected records?"),disabled:!(a(t).table.selection.length>0)},{reference:n(()=>[i("div",U,[o(_,{content:a(e)("fish.wallet.update_balance"),placement:"top"},{default:n(()=>[g((s(),p(h,{disabled:!(a(t).table.selection.length>0),class:"table-header-operate",type:"success"},{default:n(()=>[o(b,{color:"#ffffff",name:"el-icon-RefreshRight"}),i("span",$,k(a(e)("fish.wallet.update_balance")),1)]),_:1},8,["disabled"])),[[y]])]),_:1},8,["content"])])]),_:1},8,["confirm-button-text","cancel-button-text","title","disabled"])]),_:1},8,["quick-search-placeholder"]),o(q,{ref_key:"tableRef",ref:f},null,512),o(S)])}}});export{ae as default};
