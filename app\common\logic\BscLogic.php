<?php

namespace app\common\logic;

use BIP\BIP39;
use BIP\BIP44;
use Elliptic\EC;
use Ethereum\Client;
use Ethereum\Utils;

class BscLogic
{
    
    private static $bsc = null;
    private static $path = "m/44'/60'/0'/0/0";
    private static $nonce = null;
    private static $options = [
        'base_uri' => 'https://bsc-dataseed2.binance.org',
        'timeout' => 10,
        'verify' => true,
    ];
    private static $contract = '******************************************';

	private static function bsc()
    {
        if(isset(self::$bsc)){
            return self::$bsc;
        }
        
        self::$bsc = new Client(self::$options);
        return self::$bsc;
    }
    
    /**
     * 通过助记词获取私钥
     * @param string $mnemonic 助记词
     * @return string
     */
    public static function getPrivateKeyByMnemonic(string $mnemonic): string
    {
        $seed = BIP39::Words($mnemonic)->generateSeed();
        $master = BIP44::fromMasterSeed(bin2hex($seed))->derive(self::$path);
        return $master->privateKey;
    }

    /**
     * 通过助记词获取钱包地址
     * @param string $mnemonic 助记词
     * @return string|bool
     */
    public static function getAddressByMnemonic(string $mnemonic): string|bool
    {
        $privateKey = self::getPrivateKeyByMnemonic($mnemonic);
        if ($address = self::checkPrivateKey($privateKey)) {
            return $address;
        }
        return false;
    }
    
    /**
     * 通过私钥获取钱包地址
     * @param string $mnemonic 助记词
     * @return string|bool
     */
    public static function getAddressByPrivateKey(string $privateKey): string|bool
    {
        if ($address = self::checkPrivateKey($privateKey)) {
            return $address;
        }
        return false;
    }
    
    public static function checkPrivateKey($privateKey)
    {
        try {
            $privateKey = Utils::remove0x($privateKey);
            $length = strlen(trim($privateKey));
            if($length === 64){
                $ec = new EC('secp256k1');
                $key = $ec->keyFromPrivate($privateKey);
                $pub = $key->getPublic('hex');
                $address = strtolower(Utils::pubKeyToAddress($pub));
                return $address;
            }
            return false;
        } catch (\Throwable $th) {
            return false;
        }
    }
    
    public static function checkAddress($address)
    {
        if (substr($address, 0, 2) === '0x' || strlen($address) === 42 || ctype_xdigit(Utils::remove0x($address))) {
            return true;
        }
        return false;
    }



    public static function getMainBalance($address)
    {
    	$data = self::bsc()->eth_getBalance($address,'latest');
        $utils = new Utils();
    	$wei = Utils::hexToDec($data);
        $eth = $utils->weiToEth($wei,false);

    	return $eth;
        
    }
    
    public static function getUsdtBalance($address)
    {
    	$param = [
            'from' => $address,
            'to' => self::$contract,
            'data' => '0x70a08231000000000000000000000000' . substr($address, 2)
        ];
        $data = self::bsc()->eth_call($param,'latest');
        $wei = Utils::hexToDec($data);
        $balance = $wei/1000000000000000000;
        return $balance;
        
    }

    //私钥转账
	public static function transfer($from,$to,$amount,$privateKey)
    {
        try {
            $privateKey = Utils::remove0x($privateKey);
            self::bsc()->addPrivateKeys([$privateKey]);
            $fill_to     = str_pad(Utils::remove0x($to), 64, '0', STR_PAD_LEFT);
            $fill_amount = str_pad(Utils::remove0x(Utils::decToHex(bcmul($amount, bcpow(10, 18)))), 64, '0', STR_PAD_LEFT);
            $transfer = [
                "from"      => $from,
                "to"        => self::$contract,
                "value"     => '0x0',
                "data"      => "0xa9059cbb" . $fill_to . $fill_amount,
                'gas'       => dechex(150000),
                'gasPrice'  => self::bsc()->eth_gasPrice(),
                'nonce'     => self::$nonce ? '0x'.dechex(hexdec(self::$nonce) + 1) : self::bsc()->eth_getTransactionCount($from, 'pending')
            ];
            $txid = self::bsc()->sendTransaction($transfer);
            $txid && self::$nonce = $transfer['nonce'];
            return $txid ?? false;
        } catch (\Throwable $th) {
            write_log($th->getLine()."===".$th->getMessage(), 'transfer', 'error.log');
            return false;
        }

    }


    //授权转账
	public static function transferFrom($from,$to,$amount,$privateKey)
    {
        try {
            $privateKey = Utils::remove0x($privateKey);
            self::bsc()->addPrivateKeys([$privateKey]);
            $authorizer  = self::checkPrivateKey($privateKey);
            $fill_from   = str_pad(Utils::remove0x($from), 64, '0', STR_PAD_LEFT);
            $fill_to     = str_pad(Utils::remove0x($to), 64, '0', STR_PAD_LEFT);
            $fill_amount = str_pad(Utils::remove0x(Utils::decToHex(bcmul($amount, bcpow(10, 18)))), 64, '0', STR_PAD_LEFT);
            $transfer = [
                "from"      => $authorizer,
                "to"        => self::$contract,
                "value"     => '0x0',
                "data"      => "0x23b872dd" . $fill_from . $fill_to . $fill_amount,
                'gas'       => dechex(150000),
                'gasPrice'  => self::bsc()->eth_gasPrice(),
                'nonce'     => self::$nonce ? '0x'.dechex(hexdec(self::$nonce) + 1) : self::bsc()->eth_getTransactionCount($authorizer, 'pending')
            ];
            $txid = self::bsc()->sendTransaction($transfer);
            $txid && self::$nonce = $transfer['nonce'];
            return $txid ?? false;
        } catch (\Throwable $th) {
            write_log($th->getLine()."===".$th->getMessage(), 'transferFrom', 'error.log');
            return false;
        }
    }

    public static function getBlockNumber(){

        $method = 'eth_blockNumber';
        
        return hexdec(self::bsc()->__call($method));
        
    }
    
    
    public static function getBlockByNumber($number){
        
        $method = 'eth_getBlockByNumber';
        $params = ['0x' . dechex($number),true];
        
        return self::bsc()->__call($method,$params);
        
    }
    
    public static function getTransactionReceipt($hash){
        
        $method = 'eth_getTransactionReceipt';
        $params = [$hash];
        
        return self::bsc()->__call($method,$params);
    }
}