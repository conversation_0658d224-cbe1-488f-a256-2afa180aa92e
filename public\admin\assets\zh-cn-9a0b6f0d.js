const e={layouts:{"Layout configuration":"布局配置","Layout mode":"布局方式",default:"默认",classic:"经典","Single column":"单栏","Double column":"双栏","overall situation":"全局","Background page switching animation":"后台页面切换动画","Please select an animation name":"请选择动画名称",sidebar:"侧边栏","Side menu bar background color":"侧边菜单栏背景色","Side menu text color":"侧边菜单文字颜色","Side menu active item background color":"侧边菜单激活项背景色","Side menu active item text color":"侧边菜单激活项文字色","Show side menu top bar (logo bar)":"显示侧边菜单顶栏(LOGO栏)","Side menu top bar background color":"侧边菜单顶栏背景色","Side menu width (when expanded)":"侧边菜单宽度(展开时)","Side menu default icon":"侧边菜单默认图标","Side menu horizontal collapse":"侧边菜单水平折叠","Side menu accordion":"侧边菜单手风琴","Top bar":"顶栏","Top bar background color":"顶栏背景色","Top bar text color":"顶栏文字色","Background color when hovering over the top bar":"顶栏悬停时背景色","Top bar menu active item background color":"顶栏菜单激活项背景色","Top bar menu active item text color":"顶栏菜单激活项文字色","Are you sure you want to restore all configurations to the default values?":"确定要恢复全部配置到默认值吗？","Restore default":"恢复默认","personal data":"个人资料",cancellation:"注销","Dark mode":"暗黑模式","Exit full screen":"退出全屏","Full screen is not supported":"您的浏览器不支持全屏，请更换浏览器再试~"},terminal:{Terminal:"终端","Command run log":"命令运行日志","No mission yet":"还没有任务...","Test command":"测试命令","Install dependent packages":"安装依赖包",Republish:"重新发布","Clean up task list":"清理任务列表",unknown:"未知","Waiting for execution":"等待执行",Connecting:"连接中...",Executing:"执行中...","Successful execution":"执行成功","Execution failed":"执行失败","Unknown execution result":"执行结果未知","Are you sure you want to republish?":"确认要重新发布吗？","Failure to execute this command will block the execution of the queue":"本命令执行失败会阻断队列执行","Package manager":"包管理器","Please select package manager":"请选择包管理器","Switch package manager title":"只读WEB终端，可以在CRUD等操作后方便的执行 npm install、npm build 等命令，请在下方选择一个已安装好或您喜欢的NPM包管理器","I want to execute the command manually":"我想手动执行命令","Do not refresh the browser":"请勿刷新浏览器","Terminal settings":"终端设置","Install service port":"安装服务端口","The port number to start the installation service (this port needs to be opened for external network access)":"启动安装服务的端口号(外网访问则需对外开放该端口)","Installation service startup command":"安装服务启动命令","Please execute this command to start the service (add Su under Linux)":"请执行本命令以启动服务（Linux下加su）","Installation service URL":"安装服务URL","Please access the site through the installation service URL (except in debug mode)":"请通过安装服务Url访问站点（调试模式下例外）","Clean up successful tasks when starting a new task":"开始新任务时清理已成功任务","Back to terminal":"回到终端",or:"或","Site domain name":"站点域名","The current terminal is not running under the installation service, and some commands may not be executed":"当前终端未运行于安装服务下，部分命令可能无法执行。","Newly added tasks will never start because they are blocked by failed tasks":"新添加的任务永远不会开始，因为被失败的任务阻塞！（WEB终端）"}};export{e as default};
