import{z as s,u as o,l as i}from"./index-572ce0f1.js";function u(e=0){let t=e;const n={Default:70,Classic:50,Streamline:60};if(s()){const a=o();i().state.tabFullScreen||(t+=n[a.layout.layoutMode])}else t+=60;return{height:"calc(100vh - "+t.toString()+"px)"}}function l(){const e=document.querySelector(".nav-tabs");if(!e)return;const t=document.querySelector(".nav-bar"),n=document.querySelector(".nav-menus"),a=t.offsetWidth-(n.offsetWidth+20);e.style.width=a.toString()+"px"}export{u as m,l as s};
