import{d as x,L as C,_ as w}from"./index-572ce0f1.js";import{h as I,ar as L,q as i,ab as d,o as _,O as c,P as t,a7 as m,k,Z as n,V as r,z as e,m as z,_ as B,p as o}from"./vue-9f0739d1.js";const V={class:"title"},y=I({__name:"info",setup(D){const a=L("baTable"),{t:s}=x();return(N,O)=>{const l=i("el-descriptions-item"),f=i("el-tree"),p=i("el-descriptions"),b=i("el-scrollbar"),u=i("el-dialog"),g=d("drag"),h=d("zoom"),v=d("loading");return _(),c(u,{class:"ba-operate-dialog","model-value":!!e(a).form.operate,onClose:e(a).toggleForm},{header:t(()=>[m((_(),k("div",V,[n(r(e(s)("Info")),1)])),[[g,[".ba-operate-dialog",".el-dialog__header"]],[h,".ba-operate-dialog"]])]),default:t(()=>[m((_(),c(b,{class:"ba-table-form-scrollbar"},{default:t(()=>[z("div",{class:B(["ba-operate-form","ba-"+e(a).form.operate+"-form"])},[o(p,{column:2,border:""},{default:t(()=>[o(l,{label:e(s)("Id")},{default:t(()=>[n(r(e(a).form.extend.info.id),1)]),_:1},8,["label"]),o(l,{label:e(s)("auth.adminLog.Operation administrator")},{default:t(()=>[n(r(e(a).form.extend.info.username),1)]),_:1},8,["label"]),o(l,{label:e(s)("auth.adminLog.title")},{default:t(()=>[n(r(e(a).form.extend.info.title),1)]),_:1},8,["label"]),o(l,{label:e(s)("auth.adminLog.Operator IP")},{default:t(()=>[n(r(e(a).form.extend.info.ip),1)]),_:1},8,["label"]),o(l,{width:120,span:2,label:"URL"},{default:t(()=>[n(r(e(a).form.extend.info.url),1)]),_:1}),o(l,{width:120,span:2,label:"User Agent"},{default:t(()=>[n(r(e(a).form.extend.info.useragent),1)]),_:1}),o(l,{width:120,span:2,label:e(s)("Create time")},{default:t(()=>[n(r(e(C)(e(a).form.extend.info.create_time)),1)]),_:1},8,["label"]),o(l,{width:120,span:2,label:e(s)("auth.adminLog.Request data")},{default:t(()=>[o(f,{class:"table-el-tree",data:e(a).form.extend.info.data,props:{label:"label",children:"children"}},null,8,["data"])]),_:1},8,["label"])]),_:1})],2)]),_:1})),[[v,e(a).form.loading]])]),_:1},8,["model-value","onClose"])}}});const F=w(y,[["__scopeId","data-v-e3289675"]]);export{F as default};
