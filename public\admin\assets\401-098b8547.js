import{_ as d}from"./index-572ce0f1.js";import{q as a,o as i,k as l,m as s,V as n,p as o,P as t,Z as u,$ as f,a0 as m}from"./vue-9f0739d1.js";const v={},g=e=>(f("data-v-b6724a97"),e=e(),m(),e),b={class:"page"},h={class:"container"},k=g(()=>s("div",{class:"fbi"},"401 WARNING",-1)),$={class:"warning"},y={class:"page-footer"};function I(e,_){const c=a("router-link"),r=a("el-button"),p=a("el-button-group");return i(),l("div",b,[s("div",h,[k,s("div",$,n(e.$t("401.noPowerTip")),1),s("div",y,[o(p,null,{default:t(()=>[o(r,{size:"large",type:"info"},{default:t(()=>[o(c,{class:"stopcode-a",to:"/"},{default:t(()=>[u(n(e.$t("404.Return to home page")),1)]),_:1})]),_:1}),o(r,{size:"large",type:"info"},{default:t(()=>[o(c,{class:"stopcode-a",to:""},{default:t(()=>[s("span",{onClick:_[0]||(_[0]=N=>e.$router.back())},n(e.$t("404.Back to previous page")),1)]),_:1})]),_:1})]),_:1})])])])}const S=d(v,[["render",I],["__scopeId","data-v-b6724a97"]]);export{S as default};
