var ae=Object.defineProperty;var se=(s,a,t)=>a in s?ae(s,a,{enumerable:!0,configurable:!0,writable:!0,value:t}):s[a]=t;var x=(s,a,t)=>(se(s,typeof a!="symbol"?a+"":a,t),t);import{h as A,U as oe,r as L,j as F,o as r,k as h,m as l,V as y,z as m,W as N,X as R,Y as S,Z as T,_ as D,l as ne,$ as j,a0 as H,p as i,a1 as le,w as P,E as ce,n as ie,a2 as re,q as g,P as p,O as de,a3 as ue,J as pe}from"./vue-9f0739d1.js";import{_ as he,a as me}from"./avatar-75c8f563.js";import{i as k,g as fe,c as _e,_ as W,s as ge,u as be,a as ve,b as we,d as ye,e as xe,r as ke}from"./index-572ce0f1.js";import{t as Ce,l as U}from"./useDark-3f3e4e6b.js";import{b as E}from"./validate-eddfbf9e.js";const e={width:0,height:0,bubbleEl:null,canvas:null,ctx:{},circles:[],animate:!0,requestId:null},Ie=function(){e.width=window.innerWidth,e.height=window.innerHeight,e.bubbleEl=document.getElementById("bubble"),e.bubbleEl.style.height=e.height+"px",e.canvas=document.getElementById("bubble-canvas"),e.canvas.width=e.width,e.canvas.height=e.height,e.ctx=e.canvas.getContext("2d"),e.circles=[];for(let s=0;s<e.width*.5;s++){const a=new $e;e.circles.push(a)}Y(),Ve()};function K(){e.animate=!(document.body.scrollTop>e.height)}function X(){e.width=window.innerWidth,e.height=window.innerHeight,e.bubbleEl.style.height=e.height+"px",e.canvas.width=e.width,e.canvas.height=e.height}function Y(){if(e.animate){e.ctx.clearRect(0,0,e.width,e.height);for(const s in e.circles)e.circles[s].draw()}e.requestId=requestAnimationFrame(Y)}class $e{constructor(){x(this,"pos");x(this,"alpha");x(this,"scale");x(this,"velocity");x(this,"draw");this.pos={x:Math.random()*e.width,y:e.height+Math.random()*100},this.alpha=.1+Math.random()*.3,this.scale=.1+Math.random()*.3,this.velocity=Math.random(),this.draw=function(){this.pos.y-=this.velocity,this.alpha-=5e-4,e.ctx.beginPath(),e.ctx.arc(this.pos.x,this.pos.y,this.scale*10,0,2*Math.PI,!1),e.ctx.fillStyle="rgba(255,255,255,"+this.alpha+")",e.ctx.fill()}}}function Ve(){window.addEventListener("scroll",K),window.addEventListener("resize",X)}function Ee(){window.removeEventListener("scroll",K),window.removeEventListener("resize",X),cancelAnimationFrame(e.requestId)}const J=s=>(j("data-v-a7a098aa"),s=s(),H(),s),Le=["id"],ze={key:0,class:"loading"},Be={key:1,class:"captcha-img-box"},Pe=["src","alt"],Re=["onClick"],Se={key:2,class:"captcha-prompt"},Te={key:3,class:"captcha-prompt"},Me={class:"captcha-refresh-box"},qe=J(()=>l("div",{class:"captcha-refresh-line captcha-refresh-line-l"},null,-1)),Fe=["title"],De=J(()=>l("div",{class:"captcha-refresh-line captcha-refresh-line-r"},null,-1)),Ue=A({__name:"index",props:{uuid:{default:""},callback:{type:Function,default:()=>{}},class:{default:""},unset:{type:Boolean,default:!1},error:{default:k.global.t("validate.The correct area is not clicked, please try again!")},success:{default:k.global.t("validate.Verification is successful!")}},setup(s){const a=s;oe(o=>({"0aacbc72":c.value,"52eceb8a":v.value,"314b8f1b":t.captcha.width,"43514b1c":t.captcha.height}));const t=L({loading:!0,xy:[],tip:"",captcha:{id:"",text:"",base64:"",width:350,height:200}}),b=()=>{t.loading=!0,fe(a.uuid).then(o=>{t.xy=[],t.tip="",t.loading=!1,t.captcha=o.data})},$=o=>{if(t.xy.length<t.captcha.text.length&&(t.xy.push(o.offsetX+","+o.offsetY),t.xy.length==t.captcha.text.length)){const w=[t.xy.join("-"),o.target.width,o.target.height].join(";");_e(a.uuid,w,a.unset).then(()=>{t.tip=a.success,setTimeout(()=>{var d;(d=a.callback)==null||d.call(a,w),C()},1500)}).catch(()=>{t.tip=a.error,setTimeout(()=>{b()},1500)})}},V=o=>{t.xy.splice(o,1)},C=()=>{var o;(o=document.getElementById(a.uuid))==null||o.remove()},v=F(()=>(t.captcha.height+200)/2+"px"),c=F(()=>(t.captcha.width+24)/2+"px");return b(),(o,w)=>(r(),h("div",{id:o.uuid},[l("div",{class:D(["ba-click-captcha",a.class])},[t.loading?(r(),h("div",ze,y(m(k).global.t("utils.Loading")),1)):(r(),h("div",Be,[l("img",{class:"captcha-img",onClick:w[0]||(w[0]=N(d=>$(d),["prevent"])),src:t.captcha.base64,alt:m(k).global.t("validate.Captcha loading failed, please click refresh button")},null,8,Pe),(r(!0),h(R,null,S(t.xy,(d,f)=>(r(),h("span",{key:f,class:"step",onClick:z=>V(f),style:ne(`left:${parseFloat(d.split(",")[0])-13}px;top:${parseFloat(d.split(",")[1])-13}px`)},y(f+1),13,Re))),128))])),t.tip?(r(),h("div",Se,y(t.tip),1)):(r(),h("div",Te,[T(y(m(k).global.t("validate.Please click"))+" ",1),(r(!0),h(R,null,S(t.captcha.text,(d,f)=>(r(),h("span",{key:f,class:D(t.xy.length>f?"clicaptcha-clicked":"")},y(d),3))),128))])),l("div",Me,[qe,l("i",{class:"fa fa-refresh captcha-refresh-btn",title:m(k).global.t("Refresh"),onClick:b},null,8,Fe),De])],2),l("div",{class:"ba-layout-shade",onClick:C})],8,Le))}});const Ae=W(Ue,[["__scopeId","data-v-a7a098aa"]]),Ne=(s,a,t={})=>{let b=i(Ae,{uuid:s,callback:a,...t,key:ge()});le(b,document.body),b=null},M=s=>(j("data-v-ada456d4"),s=s(),H(),s),je={class:"switch-language"},He=M(()=>l("canvas",{id:"bubble-canvas",class:"bubble-canvas"},null,-1)),We=[He],Ke={class:"login"},Xe={class:"login-box"},Ye=M(()=>l("div",{class:"head"},[l("img",{src:he,alt:""})],-1)),Je={class:"form"},Oe=M(()=>l("img",{class:"profile-avatar",src:me,alt:""},null,-1)),Ze={class:"content"},Ge=A({__name:"login",setup(s){let a;const t=be(),b=ve();Ce(t.layout.isDark);const $=P(),V=P(),C=P(),v=L({showCaptcha:!1,submitLoading:!1}),c=L({username:"",password:"",keep:!1,captchaId:we(),captchaInfo:""}),{t:o}=ye(),w=L({username:[E({name:"required",message:o("login.Please enter an account")}),E({name:"account"})],password:[E({name:"required",message:o("login.Please input a password")}),E({name:"password"})]}),d=()=>{c.username===""?V.value.focus():c.password===""&&C.value.focus()};ce(()=>{a=window.setTimeout(()=>{Ie()},1e3),U("get").then(_=>{v.showCaptcha=_.data.captcha,ie(()=>d())}).catch(_=>{console.log(_)})}),re(()=>{clearTimeout(a),Ee()});const f=()=>{var _;(_=$.value)==null||_.validate(n=>{n&&(v.showCaptcha?Ne(c.captchaId,I=>z(I)):z())})},z=(_="")=>{v.submitLoading=!0,c.captchaInfo=_,U("post",c).then(n=>{b.dataFill(n.data.userInfo),pe({message:n.msg,type:"success"}),ke.push({path:n.data.routePath})}).finally(()=>{v.submitLoading=!1})};return(_,n)=>{const I=g("Icon"),O=g("el-dropdown-item"),Z=g("el-dropdown-menu"),G=g("el-dropdown"),q=g("el-input"),B=g("el-form-item"),Q=g("el-checkbox"),ee=g("el-button"),te=g("el-form");return r(),h("div",null,[l("div",je,[i(G,{size:"large","hide-timeout":50,placement:"bottom-end","hide-on-click":!0},{dropdown:p(()=>[i(Z,{class:"chang-lang"},{default:p(()=>[(r(!0),h(R,null,S(m(t).lang.langArray,u=>(r(),de(O,{key:u.name,onClick:Qe=>m(xe)(u.name)},{default:p(()=>[T(y(u.value),1)]),_:2},1032,["onClick"]))),128))]),_:1})]),default:p(()=>[i(I,{name:"fa fa-globe",color:"var(--el-text-color-secondary)",size:"28"})]),_:1})]),l("div",{onContextmenu:n[0]||(n[0]=N(()=>{},["stop"])),id:"bubble",class:"bubble"},We,32),l("div",Ke,[l("div",Xe,[Ye,l("div",Je,[Oe,l("div",Ze,[i(te,{onKeyup:n[5]||(n[5]=ue(u=>f(),["enter"])),ref_key:"formRef",ref:$,rules:w,size:"large",model:c},{default:p(()=>[i(B,{prop:"username"},{default:p(()=>[i(q,{ref_key:"usernameRef",ref:V,type:"text",clearable:"",modelValue:c.username,"onUpdate:modelValue":n[1]||(n[1]=u=>c.username=u),placeholder:m(o)("login.Please enter an account")},{prefix:p(()=>[i(I,{name:"fa fa-user",class:"form-item-icon",size:"16",color:"var(--el-input-icon-color)"})]),_:1},8,["modelValue","placeholder"])]),_:1}),i(B,{prop:"password"},{default:p(()=>[i(q,{ref_key:"passwordRef",ref:C,modelValue:c.password,"onUpdate:modelValue":n[2]||(n[2]=u=>c.password=u),type:"password",placeholder:m(o)("login.Please input a password"),"show-password":""},{prefix:p(()=>[i(I,{name:"fa fa-unlock-alt",class:"form-item-icon",size:"16",color:"var(--el-input-icon-color)"})]),_:1},8,["modelValue","placeholder"])]),_:1}),i(Q,{modelValue:c.keep,"onUpdate:modelValue":n[3]||(n[3]=u=>c.keep=u),label:m(o)("login.Hold session"),size:"default"},null,8,["modelValue","label"]),i(B,null,{default:p(()=>[i(ee,{loading:v.submitLoading,class:"submit-button",round:"",type:"primary",size:"large",onClick:n[4]||(n[4]=u=>f())},{default:p(()=>[T(y(m(o)("login.Sign in")),1)]),_:1},8,["loading"])]),_:1})]),_:1},8,["rules","model"])])])])])])}}});const lt=W(Ge,[["__scopeId","data-v-ada456d4"]]);export{lt as default};
