const t={set(e,o){window.localStorage.setItem(e,JSON.stringify(o))},get(e){const o=window.localStorage.getItem(e);return JSON.parse(o)},remove(e){window.localStorage.removeItem(e)},clear(){window.localStorage.clear()}},s={set(e,o){window.sessionStorage.setItem(e,JSON.stringify(o))},get(e){const o=window.sessionStorage.getItem(e);return JSON.parse(o)},remove(e){window.sessionStorage.removeItem(e)},clear(){window.sessionStorage.clear()}};export{t as L,s as S};
