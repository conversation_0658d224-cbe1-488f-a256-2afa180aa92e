<?php

// +----------------------------------------------------------------------
// | 缓存设置
// +----------------------------------------------------------------------

return [
    // 默认缓存驱动
    'default' => env('cache.driver', 'file'),

    // 缓存连接方式配置
    'stores'  => [
        'file' => [
            // 驱动方式
            'type'       => 'File',
            // 缓存保存目录
            'path'       => '',
            // 缓存前缀
            'prefix'     => '',
            // 缓存有效期 0表示永久缓存
            'expire'     => 0,
            // 缓存标签前缀
            'tag_prefix' => 'tag:',
            // 序列化机制 例如 ['serialize', 'unserialize']
            'serialize'  => [],
        ],
        // 更多的缓存连接
        'redis' => [
            // 驱动方式
            'type'   => env('redis.type', 'redis'),
            // 服务器地址
            'host'       => env('redis.hostname', '127.0.0.1'),
            //redis端口
            'port'       => env('redis.hostport', 6379),
            //redis密码
            'password'   => env('redis.password', ''),
            //连接超时时间
            'timeout'    => env('redis.timeout', 0),
            //数据库索引
            'select'     => env('redis.select', 0),
            //过期时间
            'expire'     => env('redis.expire', 0),
            //指定Key的前缀
            'prefix'     => env('redis.prefix', ''),
            //是否采用持久连接方式
            'persistent' => env('redis.persistent', false),
            //缓存标签前缀
            'tag_prefix' => 'tag:',
            //序列化机制 例如 ['serialize', 'unserialize']
            'serialize'  => ['serialize', 'unserialize'],
        ],
    ],
];
