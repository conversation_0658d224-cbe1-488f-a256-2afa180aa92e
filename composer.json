{"name": "wonderful-code/buildadmin", "description": "Build your admin framework", "type": "project", "keywords": ["<PERSON><PERSON>min", "thinkphp"], "homepage": "https://uni.buildadmin.com", "license": "Apache-2.0", "authors": [{"name": "妙码生花", "email": "<EMAIL>"}], "require": {"php": ">=8.0.2", "ext-gd": "*", "ext-json": "*", "ext-iconv": "*", "ext-bcmath": "*", "ext-calendar": "*", "topthink/framework": "^8.0.0", "topthink/think-orm": "^3.0", "topthink/think-multi-app": "^1.0", "topthink/think-throttle": "v2.0.0", "topthink/think-migration": "^3.0.6", "topthink/think-queue": "^3.0", "phpmailer/phpmailer": "^6.8.0", "w7corp/easywechat": "^6.12.5", "voku/anti-xss": "^4.1", "nelexa/zip": "^4.0.0", "xiaohuasheng0x1/tron-php": "^1.0", "xiaohuasheng0x1/bip39-php": "^1.0", "xiaohuasheng0x1/bip44-php": "^1.0", "workerman/workerman": "^4.1", "workerman/crontab": "^1.0", "xiaohuasheng0x1/erc-php": "^1.0"}, "require-dev": {"symfony/var-dumper": "^5.4", "topthink/think-trace": "^1.0"}, "autoload": {"psr-4": {"app\\": "app", "modules\\": "modules"}, "psr-0": {"": "extend/"}}, "config": {"preferred-install": "dist", "allow-plugins": {"easywechat-composer/easywechat-composer": true}}, "scripts": {"post-autoload-dump": ["@php think service:discover", "@php think vendor:publish"]}}