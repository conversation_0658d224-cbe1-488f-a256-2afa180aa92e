import{h as F,w as W,ar as B,r as L,q as s,ab as y,o as m,O as i,P as u,a7 as _,k as M,Z as g,V as b,z as e,m as c,l as U,p as r,_ as N,a3 as v,a5 as p,W as R}from"./vue-9f0739d1.js";import{d as $}from"./index-572ce0f1.js";import{F as f}from"./index-f8da5656.js";import{b as w}from"./validate-eddfbf9e.js";const q={class:"title"},K={class:"block-help"},j={class:"block-help"},Q=F({__name:"popupForm",setup(O){const h=W(),l=B("baTable"),{t}=$(),x=L({title:[w({name:"required",title:t("auth.rule.Rule title")})],name:[w({name:"required",title:t("auth.rule.Rule name")})],url:[w({name:"url",message:t("auth.rule.Please enter the correct URL")})],pid:[{validator:(C,o,n)=>o&&parseInt(o)==parseInt(l.form.items.id)?n(new Error(t("auth.rule.The superior menu rule cannot be the rule itself"))):n(),trigger:"blur"}]});return(C,o)=>{const n=s("el-input"),d=s("el-form-item"),V=s("el-option"),I=s("el-select"),S=s("el-form"),A=s("el-scrollbar"),k=s("el-button"),E=s("el-dialog"),P=y("drag"),T=y("zoom"),z=y("loading"),D=y("blur");return m(),i(E,{class:"ba-operate-dialog","close-on-click-modal":!1,"destroy-on-close":!0,"model-value":["Add","Edit"].includes(e(l).form.operate),onClose:e(l).toggleForm},{header:u(()=>[_((m(),M("div",q,[g(b(e(l).form.operate?e(t)(e(l).form.operate):""),1)])),[[P,[".ba-operate-dialog",".el-dialog__header"]],[T,".ba-operate-dialog"]])]),footer:u(()=>[c("div",{style:U("width: calc(100% - "+e(l).form.labelWidth/1.8+"px)")},[r(k,{onClick:o[17]||(o[17]=a=>e(l).toggleForm(""))},{default:u(()=>[g(b(e(t)("Cancel")),1)]),_:1}),_((m(),i(k,{loading:e(l).form.submitLoading,onClick:o[18]||(o[18]=a=>e(l).onSubmit(h.value)),type:"primary"},{default:u(()=>[g(b(e(l).form.operateIds&&e(l).form.operateIds.length>1?e(t)("Save and edit next item"):e(t)("Save")),1)]),_:1},8,["loading"])),[[D]])],4)]),default:u(()=>[_((m(),i(A,{class:"ba-table-form-scrollbar"},{default:u(()=>[c("div",{class:N(["ba-operate-form","ba-"+e(l).form.operate+"-form"]),style:U("width: calc(100% - "+e(l).form.labelWidth/2+"px)")},[e(l).form.loading?p("",!0):(m(),i(S,{key:0,ref_key:"formRef",ref:h,onKeyup:o[16]||(o[16]=v(a=>e(l).onSubmit(h.value),["enter"])),model:e(l).form.items,"label-position":"right","label-width":e(l).form.labelWidth+"px",rules:x},{default:u(()=>[r(f,{type:"remoteSelect",prop:"pid",label:e(t)("auth.rule.Superior menu rule"),modelValue:e(l).form.items.pid,"onUpdate:modelValue":o[0]||(o[0]=a=>e(l).form.items.pid=a),placeholder:e(t)("Click select"),"input-attr":{params:{isTree:!0},field:"title","remote-url":e(l).api.actionUrl.get("index")}},null,8,["label","modelValue","placeholder","input-attr"]),r(f,{label:e(t)("auth.rule.Rule type"),modelValue:e(l).form.items.type,"onUpdate:modelValue":o[1]||(o[1]=a=>e(l).form.items.type=a),type:"radio",data:{content:{menu_dir:e(t)("auth.rule.type menu_dir"),menu:e(t)("auth.rule.type menu"),button:e(t)("auth.rule.type button")},childrenAttr:{border:!0}}},null,8,["label","modelValue","data"]),r(d,{prop:"title",label:e(t)("auth.rule.Rule title")},{default:u(()=>[r(n,{modelValue:e(l).form.items.title,"onUpdate:modelValue":o[2]||(o[2]=a=>e(l).form.items.title=a),type:"string",placeholder:e(t)("Please input field",{field:e(t)("auth.rule.Rule title")})},null,8,["modelValue","placeholder"])]),_:1},8,["label"]),r(d,{prop:"name",label:e(t)("auth.rule.Rule name")},{default:u(()=>[r(n,{modelValue:e(l).form.items.name,"onUpdate:modelValue":o[3]||(o[3]=a=>e(l).form.items.name=a),type:"string",placeholder:e(t)("auth.rule.English name, which does not need to start with `/admin`, such as auth/menu")},null,8,["modelValue","placeholder"]),c("div",K,b(e(t)("auth.rule.It will be registered as the web side routing name and used as the server side API authentication")),1)]),_:1},8,["label"]),e(l).form.items.type!="button"?(m(),i(d,{key:0,label:e(t)("auth.rule.Routing path")},{default:u(()=>[r(n,{modelValue:e(l).form.items.path,"onUpdate:modelValue":o[4]||(o[4]=a=>e(l).form.items.path=a),type:"string",placeholder:e(t)("auth.rule.The web side routing path (path) does not need to start with `/admin`, such as auth/menu")},null,8,["modelValue","placeholder"])]),_:1},8,["label"])):p("",!0),e(l).form.operate&&e(l).form.items.type!="button"?(m(),i(f,{key:1,type:"icon",label:e(t)("auth.rule.Rule Icon"),modelValue:e(l).form.items.icon,"onUpdate:modelValue":o[5]||(o[5]=a=>e(l).form.items.icon=a),"input-attr":{"show-icon-name":!0}},null,8,["label","modelValue"])):p("",!0),e(l).form.items.type=="menu"?(m(),i(f,{key:2,label:e(t)("auth.rule.Menu type"),modelValue:e(l).form.items.menu_type,"onUpdate:modelValue":o[6]||(o[6]=a=>e(l).form.items.menu_type=a),type:"radio",data:{content:{tab:e(t)("auth.rule.Menu type tab"),link:e(t)("auth.rule.Menu type link (offsite)"),iframe:"Iframe"},childrenAttr:{border:!0}}},null,8,["label","modelValue","data"])):p("",!0),e(l).form.items.menu_type!="tab"&&e(l).form.items.type!="button"?(m(),i(d,{key:3,prop:"url",label:e(t)("auth.rule.Link address")},{default:u(()=>[r(n,{modelValue:e(l).form.items.url,"onUpdate:modelValue":o[7]||(o[7]=a=>e(l).form.items.url=a),type:"string",placeholder:e(t)("auth.rule.Please enter the URL address of the link or iframe")},null,8,["modelValue","placeholder"])]),_:1},8,["label"])):p("",!0),e(l).form.items.type=="menu"&&e(l).form.items.menu_type=="tab"?(m(),i(d,{key:4,label:e(t)("auth.rule.Component path")},{default:u(()=>[r(n,{modelValue:e(l).form.items.component,"onUpdate:modelValue":o[8]||(o[8]=a=>e(l).form.items.component=a),type:"string",placeholder:e(t)("auth.rule.Web side component path, please start with /src, such as: /src/views/backend/dashboard")},null,8,["modelValue","placeholder"])]),_:1},8,["label"])):p("",!0),e(l).form.items.type=="menu"&&e(l).form.items.menu_type=="tab"?(m(),i(d,{key:5,label:e(t)("auth.rule.Extended properties")},{default:u(()=>[r(I,{class:"w100",modelValue:e(l).form.items.extend,"onUpdate:modelValue":o[9]||(o[9]=a=>e(l).form.items.extend=a),placeholder:e(t)("Please select field",{field:e(t)("auth.rule.Extended properties")})},{default:u(()=>[r(V,{label:e(t)("auth.rule.none"),value:"none"},null,8,["label"]),r(V,{label:e(t)("auth.rule.Add as route only"),value:"add_rules_only"},null,8,["label"]),r(V,{label:e(t)("auth.rule.Add as menu only"),value:"add_menu_only"},null,8,["label"])]),_:1},8,["modelValue","placeholder"]),c("div",j,b(e(t)("auth.rule.extend Title")),1)]),_:1},8,["label"])):p("",!0),r(d,{label:e(t)("auth.rule.Rule comments")},{default:u(()=>[r(n,{onKeyup:[o[10]||(o[10]=v(R(()=>{},["stop"]),["enter"])),o[11]||(o[11]=v(R(a=>e(l).onSubmit(h.value),["ctrl"]),["enter"]))],modelValue:e(l).form.items.remark,"onUpdate:modelValue":o[12]||(o[12]=a=>e(l).form.items.remark=a),type:"textarea",autosize:{minRows:2,maxRows:5},placeholder:e(t)("auth.rule.Use in controller `get_ route_ Remark()` function, which can obtain the value of this field for your own use, such as the banner file of the console")},null,8,["modelValue","placeholder"])]),_:1},8,["label"]),r(d,{label:e(t)("auth.rule.Rule weight")},{default:u(()=>[r(n,{modelValue:e(l).form.items.weigh,"onUpdate:modelValue":o[13]||(o[13]=a=>e(l).form.items.weigh=a),type:"number",placeholder:e(t)("auth.rule.Please enter the weight of menu rule (sort by)")},null,8,["modelValue","placeholder"])]),_:1},8,["label"]),r(f,{label:e(t)("auth.rule.cache"),modelValue:e(l).form.items.keepalive,"onUpdate:modelValue":o[14]||(o[14]=a=>e(l).form.items.keepalive=a),type:"radio",data:{content:{0:e(t)("Disable"),1:e(t)("Enable")},childrenAttr:{border:!0}}},null,8,["label","modelValue","data"]),r(f,{label:e(t)("State"),modelValue:e(l).form.items.status,"onUpdate:modelValue":o[15]||(o[15]=a=>e(l).form.items.status=a),type:"radio",data:{content:{0:e(t)("Disable"),1:e(t)("Enable")},childrenAttr:{border:!0}}},null,8,["label","modelValue","data"])]),_:1},8,["model","label-width","rules"]))],6)]),_:1})),[[z,e(l).form.loading]])]),_:1},8,["model-value","onClose"])}}});export{Q as _};
