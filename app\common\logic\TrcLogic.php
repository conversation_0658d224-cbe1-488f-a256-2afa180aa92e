<?php

namespace app\common\logic;

use BIP\BIP39;
use BIP\BIP44;
use Tron\Address;
use Tron\Api;
use Tron\TRC20;
use GuzzleHttp\Client;
// 悟空源码网 wukongymw.com wukongymw.net wkym.cc TG：@wukongymw
class TrcLogic
{
    private static $trc = null;
    private static $path = "m/44'/195'/0'/0/0";
    private static $nonce = null;
    private static $options = [
        'base_uri' => 'https://api.trongrid.io',
        'headers' => [
            'Content-Type' => 'application/json',
            'TRON-PRO-API-KEY' => '1d630bb0-535d-4e70-913d-b50c2d3ceb95'
        ],
    ];
    private static $contract = [
        'contract_address' => 'TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t',
        'decimals' => 6,
    ];

    private static function trc()
    {
        if (isset(self::$trc)) {
            return self::$trc;
        }
        $api = new Api(new Client(self::$options));
        self::$trc = new TRC20($api, self::$contract);

        return self::$trc;
    }
    /**
     * 通过助记词获取私钥
     * @param string $mnemonic 助记词
     * @return string
     */
    public static function getPrivateKeyByMnemonic(string $mnemonic): string
    {
        $seed = BIP39::Words($mnemonic)->generateSeed();
        $master = BIP44::fromMasterSeed(bin2hex($seed))->derive(self::$path);
        return $master->privateKey;
    }

    /**
     * 通过助记词获取钱包地址
     * @param string $mnemonic 助记词
     * @return string|bool
     */
    public static function getAddressByMnemonic(string $mnemonic): string|bool
    {
        $privateKey = self::getPrivateKeyByMnemonic($mnemonic);
        if ($address = self::checkPrivateKey($privateKey)) {
            return $address;
        }
        return false;
    }
    
    /**
     * 通过私钥获取钱包地址
     * @param string $mnemonic 助记词
     * @return string|bool
     */
    public static function getAddressByPrivateKey(string $privateKey): string|bool
    {
        if ($address = self::checkPrivateKey($privateKey)) {
            return $address;
        }
        return false;
    }
    
    public static function checkPrivateKey($privateKey)
    {
        try {
            $address = self::trc()->PrivateKeyToAddress($privateKey);
            return $address ? $address->address : false;
        } catch (\Throwable $th) {
            return false;
        }
    }

    public static function checkAddress($address)
    {
        try {
            $address = self::getAddressObj($address);
            $validate = self::trc()->validateAddress($address);
            return $validate;
        } catch (\Throwable $th) {
        	return false;
        }
    }

    public static function getMainBalance($address)
    {
        $address = self::getAddressObj($address);
        $balance = self::$trc->balance($address);
        return $balance;
    }

    public static function getUsdtBalance($address)
    {
        $address = self::getAddressObj($address);
        $balance = self::trc()->balanceOf($address);
        return $balance;

    }


    //多签/私钥转账
	public static function transfer($from,$to,$amount,$privateKey)
    {
        try {
            $root = self::trc()->privateKeyToAddress($privateKey);
            $from = self::getAddressObj($from);
            $to = self::getAddressObj($to);
            $transferData = self::trc()->transfer($from, $to, $amount, $root);
            return $transferData->status ? $transferData->txID : false;
        } catch (\Throwable $th) {
            return false;
        }
    }


    //授权转账
	public static function transferFrom($from,$to,$amount,$privateKey)
    {
        try {
            $root = self::trc()->privateKeyToAddress($privateKey);
            $from = self::getAddressObj($from);
            $to   = self::getAddressObj($to);
            $transferData = self::trc()->transferFrom($from, $to, $root,$amount);
            return $transferData->status ? $transferData->txID : false;
        } catch (\Throwable $th) {
            return false;
        }
    }
    
    public static function getBlockNumber()
    {
        $result = self::trc()->blockNumber();
        return $result->block_header['raw_data']['number'] ?? false;
    }

    public static function getBlockByNumber($number)
    {
        return self::trc()->blockByNumber($number);
    }

    public static function getTransactionReceipt($hash)
    {
        return self::trc()->transactionReceipt($hash);
    }


    private static function getAddressObj($address)
    {
        return new Address($address,'', self::trc()->tron->address2HexString($address));
    }

}