import{h as K,w as V,r as L,q as m,ab as M,o as p,k as R,m as r,V as a,z as e,p as n,P as d,Z as c,O as _,a5 as g,l as Q,a7 as q,a3 as k,W as $,X as j,J as E,$ as J,a0 as O}from"./vue-9f0739d1.js";import{e as w,s as i,g as H,j as W}from"./crud-d10e7b25.js";import{F as X}from"./index-f8da5656.js";import{b as Y}from"./validate-eddfbf9e.js";import{_ as Z}from"./log.vue_vue_type_style_index_0_lang-e99006ad.js";import{d as A,_ as G}from"./index-572ce0f1.js";import"./index-1d626fdc.js";import"./index-d8b6d591.js";const ee=f=>(J("data-v-66c49b7b"),f=f(),O(),f),te={class:"default-main"},se={class:"crud-title"},ae={class:"start-opt"},re={class:"start-item-title"},le={class:"start-item-remark"},oe={class:"start-item suspension"},de={class:"start-item-title"},ce={class:"start-item-remark"},ne={class:"start-item suspension"},ie={class:"start-item-title"},ue={class:"start-item-remark"},pe={target:"_blank",href:"https://wonderful-code.gitee.io/guide/other/developerMustSee.html",rel:"noopener noreferrer"},me=ee(()=>r("code",null,"test_build",-1)),_e={target:"_blank",href:"https://wonderful-code.gitee.io/guide/other/developerMustSee.html",rel:"noopener noreferrer"},fe=K({__name:"start",setup(f){const{t}=A(),x=V(),v=V(),l=L({dialog:{type:"",visible:!1,dbList:[]},showLog:!1,loading:!1,successRecord:0}),I=u=>{l.dialog.type=u,l.dialog.visible=!0,u=="sql"?setTimeout(()=>{x.value.focus()},200):u=="db"&&(l.successRecord=0,i.startData.db="",H().then(s=>{l.dialog.dbList=s.data.dbs}))},N=L({db:[Y({name:"required",message:t("crud.crud.Please select a data table")})]}),h=()=>{if(v.value){if(l.dialog.type=="sql"&&!i.startData.sql){E({type:"error",message:t("crud.crud.Please enter the table creation SQL")});return}v.value.validate(u=>{u&&w(l.dialog.type)})}},U=()=>{i.startData.db&&(l.loading=!0,W(i.startData.db).then(u=>{l.successRecord=u.data.id}).finally(()=>{l.loading=!1}))},P=()=>{l.successRecord&&(i.startData.logId=l.successRecord.toString(),w("log"))},B=()=>!1;return(u,s)=>{const b=m("el-col"),D=m("el-row"),S=m("el-alert"),F=m("el-input"),T=m("el-form"),y=m("el-button"),z=m("el-dialog"),C=M("blur");return p(),R("div",te,[r("div",se,a(e(t)("crud.crud.start")),1),r("div",ae,[n(D,{gutter:20},{default:d(()=>[n(b,{xs:24,span:8},{default:d(()=>[r("div",{onClick:s[0]||(s[0]=o=>e(w)("create")),class:"start-item suspension"},[r("div",re,a(e(t)("crud.crud.create")),1),r("div",le,a(e(t)("crud.crud.New background CRUD from zero")),1)])]),_:1}),n(b,{onClick:s[1]||(s[1]=o=>I("db")),xs:24,span:8},{default:d(()=>[r("div",oe,[r("div",de,a(e(t)("crud.crud.Select Data Table")),1),r("div",ce,a(e(t)("crud.crud.Select a designed data table from the database")),1)])]),_:1}),n(b,{onClick:s[2]||(s[2]=o=>l.showLog=!0),xs:24,span:8},{default:d(()=>[r("div",ne,[r("div",ie,a(e(t)("crud.crud.CRUD record")),1),r("div",ue,a(e(t)("crud.crud.Start with previously generated CRUD code")),1)])]),_:1})]),_:1}),n(D,{justify:"center"},{default:d(()=>[n(b,{span:20,class:"ba-markdown crud-tips suspension"},{default:d(()=>[r("b",null,a(e(t)("crud.crud.Fast experience")),1),r("ol",null,[r("li",null,[c(a(e(t)("crud.crud.experience 1 1"))+" ",1),r("a",pe,a(e(t)("crud.crud.experience 1 2")),1),c(" "+a(e(t)("crud.crud.experience 1 3")),1)]),r("li",null,[c(a(e(t)("crud.crud.experience 2 1"))+" ",1),r("code",null,a(e(t)("crud.crud.experience 2 2")),1),c(" "+a(e(t)("crud.crud.experience 2 3")),1),me,c(a(e(t)("crud.crud.data sheet")),1)]),r("li",null,[c(a(e(t)("crud.crud.experience 3 1")),1),r("code",null,a(e(t)("crud.crud.experience 3 2")),1),c(" "+a(e(t)("crud.crud.experience 3 3"))+" ",1),r("code",null,a(e(t)("crud.crud.experience 3 4")),1)])]),B()?g("",!0):(p(),_(S,{key:0,class:"no-dev",type:"warning","show-icon":!0,closable:!1},{title:d(()=>[r("span",null,a(e(t)("crud.crud.experience 4 1")),1),r("a",_e,a(e(t)("crud.crud.experience 4 2")),1),r("span",null,[c(a(e(t)("crud.crud.experience 4 3")),1),r("code",null,a(e(t)("crud.crud.experience 4 4")),1)])]),_:1}))]),_:1})]),_:1}),n(z,{class:"ba-operate-dialog select-db-dialog",modelValue:l.dialog.visible,"onUpdate:modelValue":s[10]||(s[10]=o=>l.dialog.visible=o),title:l.dialog.type=="sql"?e(t)("crud.crud.Please enter SQL"):e(t)("crud.crud.Please select a data table"),"destroy-on-close":!0},{footer:d(()=>[r("div",{style:Q({width:"calc(100% * 0.9)"})},[n(y,{onClick:s[8]||(s[8]=o=>l.dialog.visible=!1)},{default:d(()=>[c(a(u.$t("Cancel")),1)]),_:1}),q((p(),_(y,{loading:l.loading,onClick:s[9]||(s[9]=o=>h()),type:"primary"},{default:d(()=>[c(a(e(t)("Confirm")),1)]),_:1},8,["loading"])),[[C]]),l.successRecord?q((p(),_(y,{key:0,onClick:P,type:"success"},{default:d(()=>[c(a(e(t)("crud.crud.Start with the historical record")),1)]),_:1})),[[C]]):g("",!0)],4)]),default:d(()=>[n(T,{"label-width":140,onKeyup:s[7]||(s[7]=k(o=>h(),["enter"])),class:"select-db-form",ref_key:"formRef",ref:v,model:e(i).startData,rules:N},{default:d(()=>[l.dialog.type=="sql"?(p(),_(F,{key:0,class:"sql-input",prop:"sql",ref_key:"sqlInputRef",ref:x,modelValue:e(i).startData.sql,"onUpdate:modelValue":s[3]||(s[3]=o=>e(i).startData.sql=o),type:"textarea",placeholder:e(t)("crud.crud.table create SQL"),rows:10,onKeyup:[s[4]||(s[4]=k($(()=>{},["stop"]),["enter"])),s[5]||(s[5]=k($(o=>h(),["ctrl"]),["enter"]))]},null,8,["modelValue","placeholder"])):l.dialog.type=="db"?(p(),R(j,{key:1},[(p(),_(X,{label:e(t)("crud.crud.data sheet"),class:"select-db",modelValue:e(i).startData.db,"onUpdate:modelValue":s[6]||(s[6]=o=>e(i).startData.db=o),type:"select",key:JSON.stringify(l.dialog.dbList),placeholder:e(t)("crud.crud.Please select a data table"),data:{content:l.dialog.dbList},attr:{blockHelp:e(t)("crud.crud.data sheet help")},"input-attr":{onChange:U},prop:"db"},null,8,["label","modelValue","placeholder","data","attr","input-attr"])),l.successRecord?(p(),_(S,{key:0,class:"success-record-alert",title:e(t)("crud.crud.The selected table has already generated records You are advised to start with historical records"),"show-icon":!0,closable:!1,type:"warning"},null,8,["title"])):g("",!0)],64)):g("",!0)]),_:1},8,["model","rules"])]),_:1},8,["modelValue","title"]),n(Z,{modelValue:l.showLog,"onUpdate:modelValue":s[11]||(s[11]=o=>l.showLog=o)},null,8,["modelValue"])])])}}});const De=G(fe,[["__scopeId","data-v-66c49b7b"]]);export{De as default};
