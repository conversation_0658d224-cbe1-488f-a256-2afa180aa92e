const e={Id:"ID",State:"State",Home:"Home",Complete:"Completed",Edit:"Edit",Add:"Add",Info:"Details",Delete:"Delete",Refresh:"Refresh",Operate:"Operate",Confirm:"Confirm",Cancel:"Cancel",Save:"Save",Upload:"Upload",Retry:"Retry",Reminder:"Reminder",Disable:"Disable",Enable:"Enable",Shrink:"Shrink",Open:"Open",Search:"Search",Reset:"Reset",To:"To",None:"None",Unknown:"Unknown",Weigh:"weigh","Drag sort":"Drag sort","Save and edit next item":"save and edit next item","Quick search placeholder":"Fuzzy search by {fields}","Please select field":"Please select {field}","Please input field":"Please input {field}","Please enter the correct field":"Please enter the correct {field}","Update time":"Update time","Create time":"Create time","Fuzzy query":"Fuzzy query","Click select":"Click select","Edit selected row":"Edit selected row","Delete selected row":"Delete selected row","Are you sure to delete the selected record?":"Are you sure to delete the selected record?","All submenus":"All submenus","Shrink all":"Shrinkage all","Expand all":"Expand all","Expand generic search":"Expand Universal Search","Link address":"Link address","No route found to jump~":"Failed to find a jump route.","This is a deliberate error thrown to prevent a hot update of Vite":"This is a deliberate error thrown to prevent a hot update of Vite"};export{e as default};
