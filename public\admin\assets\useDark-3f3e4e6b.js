import{f as o,a as s,h as a,j as r,u}from"./index-572ce0f1.js";const n="/admin/Index/";function g(){return o({url:n+"index",method:"get"})}function d(t,e={}){return o({url:n+"login",data:e,method:t})}function f(){const t=s();return o({url:n+"logout",method:"POST",data:{refreshToken:t.getToken("refresh")}})}const i=a({onChanged(t){const e=u();l(t),e.setLayout("isDark",t),e.onSetLayoutColor()}}),m=r(i);function l(t){const e=document.getElementsByTagName("html")[0];t?e.setAttribute("class","dark"):e.setAttribute("class","")}export{f as a,g as i,d as l,m as t};
