{"compilerOptions": {"target": "esnext", "useDefineForClassFields": true, "module": "esnext", "moduleResolution": "node", "removeComments": false, "strict": true, "jsx": "preserve", "sourceMap": false, "resolveJsonModule": true, "esModuleInterop": true, "lib": ["esnext", "dom"], "isolatedModules": true, "baseUrl": "./", "paths": {"/@/*": ["src/*"]}, "types": ["element-plus/global"]}, "exclude": ["node_modules"]}