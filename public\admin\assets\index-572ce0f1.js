var zr=Object.defineProperty;var Wr=(e,t,n)=>t in e?zr(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n;var pe=(e,t,n)=>(Wr(e,typeof t!="symbol"?t+"":t,n),n);import{g as Hr,v as qr,c as Gr,s as Jr,a as Yr,b as Bt,n as X,d as ke,r as Me,i as vt,e as Kr,f as Xr,u as Qr,h as Ut,j as Y,o as wt,k as en,l as tn,m as Zr,p as be,q as Et,t as ea,w as oe,x as ta,y as na,z as Bn,A as ra,B as Un,C as aa,D as $n,E as $t,F as fe,G as oa,H as sa,I as ia,J as we,K as la,L as ca,M as Vn,N as ua,O as fa,P as da,Q as ma,R as ga,S as pa,T as ha}from"./vue-9f0739d1.js";(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const a of document.querySelectorAll('link[rel="modulepreload"]'))r(a);new MutationObserver(a=>{for(const o of a)if(o.type==="childList")for(const s of o.addedNodes)s.tagName==="LINK"&&s.rel==="modulepreload"&&r(s)}).observe(document,{childList:!0,subtree:!0});function n(a){const o={};return a.integrity&&(o.integrity=a.integrity),a.referrerPolicy&&(o.referrerPolicy=a.referrerPolicy),a.crossOrigin==="use-credentials"?o.credentials="include":a.crossOrigin==="anonymous"?o.credentials="omit":o.credentials="same-origin",o}function r(a){if(a.ep)return;a.ep=!0;const o=n(a);fetch(a.href,o)}})();var Q={};const ba=Hr(qr);/*!
  * vue-i18n v9.2.2
  * (c) 2022 kazuya kawaguchi
  * Released under the MIT License.
  */Object.defineProperty(Q,"__esModule",{value:!0});var R=Gr,N=ba,f=Jr;const zn="9.2.2";let Wn=R.CompileErrorCodes.__EXTEND_POINT__;const W=()=>++Wn,B={UNEXPECTED_RETURN_TYPE:Wn,INVALID_ARGUMENT:W(),MUST_BE_CALL_SETUP_TOP:W(),NOT_INSLALLED:W(),NOT_AVAILABLE_IN_LEGACY_MODE:W(),REQUIRED_VALUE:W(),INVALID_VALUE:W(),CANNOT_SETUP_VUE_DEVTOOLS_PLUGIN:W(),NOT_INSLALLED_WITH_PROVIDE:W(),UNEXPECTED_ERROR:W(),NOT_COMPATIBLE_LEGACY_VUE_I18N:W(),BRIDGE_SUPPORT_VUE_2_ONLY:W(),MUST_DEFINE_I18N_OPTION_IN_ALLOW_COMPOSITION:W(),NOT_AVAILABLE_COMPOSITION_IN_LEGACY:W(),__EXTEND_POINT__:W()};function U(e,...t){return R.createCompileError(e,null,void 0)}const kt=f.makeSymbol("__transrateVNode"),Tt=f.makeSymbol("__datetimeParts"),Ot=f.makeSymbol("__numberParts"),Hn=f.makeSymbol("__setPluralRules");f.makeSymbol("__intlifyMeta");const qn=f.makeSymbol("__injectWithOption"),_a="__VUE_I18N_BRIDGE__";function St(e){if(!f.isObject(e))return e;for(const t in e)if(f.hasOwn(e,t))if(!t.includes("."))f.isObject(e[t])&&St(e[t]);else{const n=t.split("."),r=n.length-1;let a=e;for(let o=0;o<r;o++)n[o]in a||(a[n[o]]={}),a=a[n[o]];a[n[r]]=e[t],delete e[t],f.isObject(a[n[r]])&&St(a[n[r]])}return e}function Ue(e,t){const{messages:n,__i18n:r,messageResolver:a,flatJson:o}=t,s=f.isPlainObject(n)?n:f.isArray(r)?{}:{[e]:{}};if(f.isArray(r)&&r.forEach(i=>{if("locale"in i&&"resource"in i){const{locale:c,resource:l}=i;c?(s[c]=s[c]||{},_e(l,s[c])):_e(l,s)}else f.isString(i)&&_e(JSON.parse(i),s)}),a==null&&o)for(const i in s)f.hasOwn(s,i)&&St(s[i]);return s}const Fe=e=>!f.isObject(e)||f.isArray(e);function _e(e,t){if(Fe(e)||Fe(t))throw U(B.INVALID_VALUE);for(const n in e)f.hasOwn(e,n)&&(Fe(e[n])||Fe(t[n])?t[n]=e[n]:_e(e[n],t[n]))}function ya(e){return e.type}function Gn(e,t,n){let r=f.isObject(t.messages)?t.messages:{};"__i18nGlobal"in n&&(r=Ue(e.locale.value,{messages:r,__i18n:n.__i18nGlobal}));const a=Object.keys(r);a.length&&a.forEach(o=>{e.mergeLocaleMessage(o,r[o])});{if(f.isObject(t.datetimeFormats)){const o=Object.keys(t.datetimeFormats);o.length&&o.forEach(s=>{e.mergeDateTimeFormat(s,t.datetimeFormats[s])})}if(f.isObject(t.numberFormats)){const o=Object.keys(t.numberFormats);o.length&&o.forEach(s=>{e.mergeNumberFormat(s,t.numberFormats[s])})}}}function nn(e){return N.createVNode(N.Text,null,e,0)}let rn=0;function an(e){return(t,n,r,a)=>e(n,r,N.getCurrentInstance()||void 0,a)}function Vt(e={},t){const{__root:n}=e,r=n===void 0;let a=f.isBoolean(e.inheritLocale)?e.inheritLocale:!0;const o=N.ref(n&&a?n.locale.value:f.isString(e.locale)?e.locale:R.DEFAULT_LOCALE),s=N.ref(n&&a?n.fallbackLocale.value:f.isString(e.fallbackLocale)||f.isArray(e.fallbackLocale)||f.isPlainObject(e.fallbackLocale)||e.fallbackLocale===!1?e.fallbackLocale:o.value),i=N.ref(Ue(o.value,e)),c=N.ref(f.isPlainObject(e.datetimeFormats)?e.datetimeFormats:{[o.value]:{}}),l=N.ref(f.isPlainObject(e.numberFormats)?e.numberFormats:{[o.value]:{}});let d=n?n.missingWarn:f.isBoolean(e.missingWarn)||f.isRegExp(e.missingWarn)?e.missingWarn:!0,h=n?n.fallbackWarn:f.isBoolean(e.fallbackWarn)||f.isRegExp(e.fallbackWarn)?e.fallbackWarn:!0,E=n?n.fallbackRoot:f.isBoolean(e.fallbackRoot)?e.fallbackRoot:!0,v=!!e.fallbackFormat,u=f.isFunction(e.missing)?e.missing:null,m=f.isFunction(e.missing)?an(e.missing):null,k=f.isFunction(e.postTranslation)?e.postTranslation:null,O=n?n.warnHtmlMessage:f.isBoolean(e.warnHtmlMessage)?e.warnHtmlMessage:!0,P=!!e.escapeParameter;const F=n?n.modifiers:f.isPlainObject(e.modifiers)?e.modifiers:{};let S=e.pluralRules||n&&n.pluralRules,T;T=(()=>{r&&R.setFallbackContext(null);const p={version:zn,locale:o.value,fallbackLocale:s.value,messages:i.value,modifiers:F,pluralRules:S,missing:m===null?void 0:m,missingWarn:d,fallbackWarn:h,fallbackFormat:v,unresolving:!0,postTranslation:k===null?void 0:k,warnHtmlMessage:O,escapeParameter:P,messageResolver:e.messageResolver,__meta:{framework:"vue"}};p.datetimeFormats=c.value,p.numberFormats=l.value,p.__datetimeFormatters=f.isPlainObject(T)?T.__datetimeFormatters:void 0,p.__numberFormatters=f.isPlainObject(T)?T.__numberFormatters:void 0;const y=R.createCoreContext(p);return r&&R.setFallbackContext(y),y})(),R.updateFallbackLocale(T,o.value,s.value);function b(){return[o.value,s.value,i.value,c.value,l.value]}const _=N.computed({get:()=>o.value,set:p=>{o.value=p,T.locale=o.value}}),A=N.computed({get:()=>s.value,set:p=>{s.value=p,T.fallbackLocale=s.value,R.updateFallbackLocale(T,o.value,p)}}),I=N.computed(()=>i.value),se=N.computed(()=>c.value),Ke=N.computed(()=>l.value);function Xe(){return f.isFunction(k)?k:null}function Qe(p){k=p,T.postTranslation=p}function Ze(){return u}function te(p){p!==null&&(m=an(p)),u=p,T.missing=m}const ne=(p,y,J,z,mt,Re)=>{b();let ge;if(ge=p(T),f.isNumber(ge)&&ge===R.NOT_REOSLVED){const[Vr,Rl]=y();return n&&E?z(n):mt(Vr)}else{if(Re(ge))return ge;throw U(B.UNEXPECTED_RETURN_TYPE)}};function Le(...p){return ne(y=>Reflect.apply(R.translate,null,[y,...p]),()=>R.parseTranslateArgs(...p),"translate",y=>Reflect.apply(y.t,y,[...p]),y=>y,y=>f.isString(y))}function et(...p){const[y,J,z]=p;if(z&&!f.isObject(z))throw U(B.INVALID_ARGUMENT);return Le(y,J,f.assign({resolvedMessage:!0},z||{}))}function tt(...p){return ne(y=>Reflect.apply(R.datetime,null,[y,...p]),()=>R.parseDateTimeArgs(...p),"datetime format",y=>Reflect.apply(y.d,y,[...p]),()=>R.MISSING_RESOLVE_VALUE,y=>f.isString(y))}function nt(...p){return ne(y=>Reflect.apply(R.number,null,[y,...p]),()=>R.parseNumberArgs(...p),"number format",y=>Reflect.apply(y.n,y,[...p]),()=>R.MISSING_RESOLVE_VALUE,y=>f.isString(y))}function rt(p){return p.map(y=>f.isString(y)||f.isNumber(y)||f.isBoolean(y)?nn(String(y)):y)}const at={normalize:rt,interpolate:p=>p,type:"vnode"};function ot(...p){return ne(y=>{let J;const z=y;try{z.processor=at,J=Reflect.apply(R.translate,null,[z,...p])}finally{z.processor=null}return J},()=>R.parseTranslateArgs(...p),"translate",y=>y[kt](...p),y=>[nn(y)],y=>f.isArray(y))}function st(...p){return ne(y=>Reflect.apply(R.number,null,[y,...p]),()=>R.parseNumberArgs(...p),"number format",y=>y[Ot](...p),()=>[],y=>f.isString(y)||f.isArray(y))}function it(...p){return ne(y=>Reflect.apply(R.datetime,null,[y,...p]),()=>R.parseDateTimeArgs(...p),"datetime format",y=>y[Tt](...p),()=>[],y=>f.isString(y)||f.isArray(y))}function lt(p){S=p,T.pluralRules=S}function ct(p,y){const J=f.isString(y)?y:o.value,z=Pe(J);return T.messageResolver(z,p)!==null}function ut(p){let y=null;const J=R.fallbackWithLocaleChain(T,s.value,o.value);for(let z=0;z<J.length;z++){const mt=i.value[J[z]]||{},Re=T.messageResolver(mt,p);if(Re!=null){y=Re;break}}return y}function ft(p){const y=ut(p);return y??(n?n.tm(p)||{}:{})}function Pe(p){return i.value[p]||{}}function dt(p,y){i.value[p]=y,T.messages=i.value}function w(p,y){i.value[p]=i.value[p]||{},_e(y,i.value[p]),T.messages=i.value}function M(p){return c.value[p]||{}}function Mr(p,y){c.value[p]=y,T.datetimeFormats=c.value,R.clearDateTimeFormat(T,p,y)}function jr(p,y){c.value[p]=f.assign(c.value[p]||{},y),T.datetimeFormats=c.value,R.clearDateTimeFormat(T,p,y)}function Br(p){return l.value[p]||{}}function Ur(p,y){l.value[p]=y,T.numberFormats=l.value,R.clearNumberFormat(T,p,y)}function $r(p,y){l.value[p]=f.assign(l.value[p]||{},y),T.numberFormats=l.value,R.clearNumberFormat(T,p,y)}rn++,n&&f.inBrowser&&(N.watch(n.locale,p=>{a&&(o.value=p,T.locale=p,R.updateFallbackLocale(T,o.value,s.value))}),N.watch(n.fallbackLocale,p=>{a&&(s.value=p,T.fallbackLocale=p,R.updateFallbackLocale(T,o.value,s.value))}));const V={id:rn,locale:_,fallbackLocale:A,get inheritLocale(){return a},set inheritLocale(p){a=p,p&&n&&(o.value=n.locale.value,s.value=n.fallbackLocale.value,R.updateFallbackLocale(T,o.value,s.value))},get availableLocales(){return Object.keys(i.value).sort()},messages:I,get modifiers(){return F},get pluralRules(){return S||{}},get isGlobal(){return r},get missingWarn(){return d},set missingWarn(p){d=p,T.missingWarn=d},get fallbackWarn(){return h},set fallbackWarn(p){h=p,T.fallbackWarn=h},get fallbackRoot(){return E},set fallbackRoot(p){E=p},get fallbackFormat(){return v},set fallbackFormat(p){v=p,T.fallbackFormat=v},get warnHtmlMessage(){return O},set warnHtmlMessage(p){O=p,T.warnHtmlMessage=p},get escapeParameter(){return P},set escapeParameter(p){P=p,T.escapeParameter=p},t:Le,getLocaleMessage:Pe,setLocaleMessage:dt,mergeLocaleMessage:w,getPostTranslationHandler:Xe,setPostTranslationHandler:Qe,getMissingHandler:Ze,setMissingHandler:te,[Hn]:lt};return V.datetimeFormats=se,V.numberFormats=Ke,V.rt=et,V.te=ct,V.tm=ft,V.d=tt,V.n=nt,V.getDateTimeFormat=M,V.setDateTimeFormat=Mr,V.mergeDateTimeFormat=jr,V.getNumberFormat=Br,V.setNumberFormat=Ur,V.mergeNumberFormat=$r,V[qn]=e.__injectWithOption,V[kt]=ot,V[Tt]=it,V[Ot]=st,V}function va(e){const t=f.isString(e.locale)?e.locale:R.DEFAULT_LOCALE,n=f.isString(e.fallbackLocale)||f.isArray(e.fallbackLocale)||f.isPlainObject(e.fallbackLocale)||e.fallbackLocale===!1?e.fallbackLocale:t,r=f.isFunction(e.missing)?e.missing:void 0,a=f.isBoolean(e.silentTranslationWarn)||f.isRegExp(e.silentTranslationWarn)?!e.silentTranslationWarn:!0,o=f.isBoolean(e.silentFallbackWarn)||f.isRegExp(e.silentFallbackWarn)?!e.silentFallbackWarn:!0,s=f.isBoolean(e.fallbackRoot)?e.fallbackRoot:!0,i=!!e.formatFallbackMessages,c=f.isPlainObject(e.modifiers)?e.modifiers:{},l=e.pluralizationRules,d=f.isFunction(e.postTranslation)?e.postTranslation:void 0,h=f.isString(e.warnHtmlInMessage)?e.warnHtmlInMessage!=="off":!0,E=!!e.escapeParameterHtml,v=f.isBoolean(e.sync)?e.sync:!0;let u=e.messages;if(f.isPlainObject(e.sharedMessages)){const T=e.sharedMessages;u=Object.keys(T).reduce((b,_)=>{const A=b[_]||(b[_]={});return f.assign(A,T[_]),b},u||{})}const{__i18n:m,__root:k,__injectWithOption:O}=e,P=e.datetimeFormats,F=e.numberFormats,S=e.flatJson;return{locale:t,fallbackLocale:n,messages:u,flatJson:S,datetimeFormats:P,numberFormats:F,missing:r,missingWarn:a,fallbackWarn:o,fallbackRoot:s,fallbackFormat:i,modifiers:c,pluralRules:l,postTranslation:d,warnHtmlMessage:h,escapeParameter:E,messageResolver:e.messageResolver,inheritLocale:v,__i18n:m,__root:k,__injectWithOption:O}}function Lt(e={},t){{const n=Vt(va(e)),r={id:n.id,get locale(){return n.locale.value},set locale(a){n.locale.value=a},get fallbackLocale(){return n.fallbackLocale.value},set fallbackLocale(a){n.fallbackLocale.value=a},get messages(){return n.messages.value},get datetimeFormats(){return n.datetimeFormats.value},get numberFormats(){return n.numberFormats.value},get availableLocales(){return n.availableLocales},get formatter(){return{interpolate(){return[]}}},set formatter(a){},get missing(){return n.getMissingHandler()},set missing(a){n.setMissingHandler(a)},get silentTranslationWarn(){return f.isBoolean(n.missingWarn)?!n.missingWarn:n.missingWarn},set silentTranslationWarn(a){n.missingWarn=f.isBoolean(a)?!a:a},get silentFallbackWarn(){return f.isBoolean(n.fallbackWarn)?!n.fallbackWarn:n.fallbackWarn},set silentFallbackWarn(a){n.fallbackWarn=f.isBoolean(a)?!a:a},get modifiers(){return n.modifiers},get formatFallbackMessages(){return n.fallbackFormat},set formatFallbackMessages(a){n.fallbackFormat=a},get postTranslation(){return n.getPostTranslationHandler()},set postTranslation(a){n.setPostTranslationHandler(a)},get sync(){return n.inheritLocale},set sync(a){n.inheritLocale=a},get warnHtmlInMessage(){return n.warnHtmlMessage?"warn":"off"},set warnHtmlInMessage(a){n.warnHtmlMessage=a!=="off"},get escapeParameterHtml(){return n.escapeParameter},set escapeParameterHtml(a){n.escapeParameter=a},get preserveDirectiveContent(){return!0},set preserveDirectiveContent(a){},get pluralizationRules(){return n.pluralRules||{}},__composer:n,t(...a){const[o,s,i]=a,c={};let l=null,d=null;if(!f.isString(o))throw U(B.INVALID_ARGUMENT);const h=o;return f.isString(s)?c.locale=s:f.isArray(s)?l=s:f.isPlainObject(s)&&(d=s),f.isArray(i)?l=i:f.isPlainObject(i)&&(d=i),Reflect.apply(n.t,n,[h,l||d||{},c])},rt(...a){return Reflect.apply(n.rt,n,[...a])},tc(...a){const[o,s,i]=a,c={plural:1};let l=null,d=null;if(!f.isString(o))throw U(B.INVALID_ARGUMENT);const h=o;return f.isString(s)?c.locale=s:f.isNumber(s)?c.plural=s:f.isArray(s)?l=s:f.isPlainObject(s)&&(d=s),f.isString(i)?c.locale=i:f.isArray(i)?l=i:f.isPlainObject(i)&&(d=i),Reflect.apply(n.t,n,[h,l||d||{},c])},te(a,o){return n.te(a,o)},tm(a){return n.tm(a)},getLocaleMessage(a){return n.getLocaleMessage(a)},setLocaleMessage(a,o){n.setLocaleMessage(a,o)},mergeLocaleMessage(a,o){n.mergeLocaleMessage(a,o)},d(...a){return Reflect.apply(n.d,n,[...a])},getDateTimeFormat(a){return n.getDateTimeFormat(a)},setDateTimeFormat(a,o){n.setDateTimeFormat(a,o)},mergeDateTimeFormat(a,o){n.mergeDateTimeFormat(a,o)},n(...a){return Reflect.apply(n.n,n,[...a])},getNumberFormat(a){return n.getNumberFormat(a)},setNumberFormat(a,o){n.setNumberFormat(a,o)},mergeNumberFormat(a,o){n.mergeNumberFormat(a,o)},getChoiceIndex(a,o){return-1},__onComponentInstanceCreated(a){const{componentInstanceCreatedListener:o}=e;o&&o(a,r)}};return r}}const zt={tag:{type:[String,Object]},locale:{type:String},scope:{type:String,validator:e=>e==="parent"||e==="global",default:"parent"},i18n:{type:Object}};function wa({slots:e},t){return t.length===1&&t[0]==="default"?(e.default?e.default():[]).reduce((r,a)=>r=[...r,...f.isArray(a.children)?a.children:[a]],[]):t.reduce((n,r)=>{const a=e[r];return a&&(n[r]=a()),n},{})}function Jn(e){return N.Fragment}const Pt={name:"i18n-t",props:f.assign({keypath:{type:String,required:!0},plural:{type:[Number,String],validator:e=>f.isNumber(e)||!isNaN(e)}},zt),setup(e,t){const{slots:n,attrs:r}=t,a=e.i18n||$e({useScope:e.scope,__useComponent:!0});return()=>{const o=Object.keys(n).filter(h=>h!=="_"),s={};e.locale&&(s.locale=e.locale),e.plural!==void 0&&(s.plural=f.isString(e.plural)?+e.plural:e.plural);const i=wa(t,o),c=a[kt](e.keypath,i,s),l=f.assign({},r),d=f.isString(e.tag)||f.isObject(e.tag)?e.tag:Jn();return N.h(d,l,c)}}};function Ea(e){return f.isArray(e)&&!f.isString(e[0])}function Yn(e,t,n,r){const{slots:a,attrs:o}=t;return()=>{const s={part:!0};let i={};e.locale&&(s.locale=e.locale),f.isString(e.format)?s.key=e.format:f.isObject(e.format)&&(f.isString(e.format.key)&&(s.key=e.format.key),i=Object.keys(e.format).reduce((E,v)=>n.includes(v)?f.assign({},E,{[v]:e.format[v]}):E,{}));const c=r(e.value,s,i);let l=[s.key];f.isArray(c)?l=c.map((E,v)=>{const u=a[E.type],m=u?u({[E.type]:E.value,index:v,parts:c}):[E.value];return Ea(m)&&(m[0].key=`${E.type}-${v}`),m}):f.isString(c)&&(l=[c]);const d=f.assign({},o),h=f.isString(e.tag)||f.isObject(e.tag)?e.tag:Jn();return N.h(h,d,l)}}const Rt={name:"i18n-n",props:f.assign({value:{type:Number,required:!0},format:{type:[String,Object]}},zt),setup(e,t){const n=e.i18n||$e({useScope:"parent",__useComponent:!0});return Yn(e,t,R.NUMBER_FORMAT_OPTIONS_KEYS,(...r)=>n[Ot](...r))}},Ft={name:"i18n-d",props:f.assign({value:{type:[Number,Date],required:!0},format:{type:[String,Object]}},zt),setup(e,t){const n=e.i18n||$e({useScope:"parent",__useComponent:!0});return Yn(e,t,R.DATETIME_FORMAT_OPTIONS_KEYS,(...r)=>n[Tt](...r))}};function ka(e,t){const n=e;if(e.mode==="composition")return n.__getInstance(t)||e.global;{const r=n.__getInstance(t);return r!=null?r.__composer:e.global.__composer}}function Kn(e){const t=s=>{const{instance:i,modifiers:c,value:l}=s;if(!i||!i.$)throw U(B.UNEXPECTED_ERROR);const d=ka(e,i.$),h=on(l);return[Reflect.apply(d.t,d,[...sn(h)]),d]};return{created:(s,i)=>{const[c,l]=t(i);f.inBrowser&&e.global===l&&(s.__i18nWatcher=N.watch(l.locale,()=>{i.instance&&i.instance.$forceUpdate()})),s.__composer=l,s.textContent=c},unmounted:s=>{f.inBrowser&&s.__i18nWatcher&&(s.__i18nWatcher(),s.__i18nWatcher=void 0,delete s.__i18nWatcher),s.__composer&&(s.__composer=void 0,delete s.__composer)},beforeUpdate:(s,{value:i})=>{if(s.__composer){const c=s.__composer,l=on(i);s.textContent=Reflect.apply(c.t,c,[...sn(l)])}},getSSRProps:s=>{const[i]=t(s);return{textContent:i}}}}function on(e){if(f.isString(e))return{path:e};if(f.isPlainObject(e)){if(!("path"in e))throw U(B.REQUIRED_VALUE,"path");return e}else throw U(B.INVALID_VALUE)}function sn(e){const{path:t,locale:n,args:r,choice:a,plural:o}=e,s={},i=r||{};return f.isString(n)&&(s.locale=n),f.isNumber(a)&&(s.plural=a),f.isNumber(o)&&(s.plural=o),[t,i,s]}function Ta(e,t,...n){const r=f.isPlainObject(n[0])?n[0]:{},a=!!r.useI18nComponentName;(f.isBoolean(r.globalInstall)?r.globalInstall:!0)&&(e.component(a?"i18n":Pt.name,Pt),e.component(Rt.name,Rt),e.component(Ft.name,Ft)),e.directive("t",Kn(t))}function Oa(e,t,n){return{beforeCreate(){const r=N.getCurrentInstance();if(!r)throw U(B.UNEXPECTED_ERROR);const a=this.$options;if(a.i18n){const o=a.i18n;a.__i18n&&(o.__i18n=a.__i18n),o.__root=t,this===this.$root?this.$i18n=ln(e,o):(o.__injectWithOption=!0,this.$i18n=Lt(o))}else a.__i18n?this===this.$root?this.$i18n=ln(e,a):this.$i18n=Lt({__i18n:a.__i18n,__injectWithOption:!0,__root:t}):this.$i18n=e;a.__i18nGlobal&&Gn(t,a,a),e.__onComponentInstanceCreated(this.$i18n),n.__setInstance(r,this.$i18n),this.$t=(...o)=>this.$i18n.t(...o),this.$rt=(...o)=>this.$i18n.rt(...o),this.$tc=(...o)=>this.$i18n.tc(...o),this.$te=(o,s)=>this.$i18n.te(o,s),this.$d=(...o)=>this.$i18n.d(...o),this.$n=(...o)=>this.$i18n.n(...o),this.$tm=o=>this.$i18n.tm(o)},mounted(){},unmounted(){const r=N.getCurrentInstance();if(!r)throw U(B.UNEXPECTED_ERROR);delete this.$t,delete this.$rt,delete this.$tc,delete this.$te,delete this.$d,delete this.$n,delete this.$tm,n.__deleteInstance(r),delete this.$i18n}}}function ln(e,t){e.locale=t.locale||e.locale,e.fallbackLocale=t.fallbackLocale||e.fallbackLocale,e.missing=t.missing||e.missing,e.silentTranslationWarn=t.silentTranslationWarn||e.silentFallbackWarn,e.silentFallbackWarn=t.silentFallbackWarn||e.silentFallbackWarn,e.formatFallbackMessages=t.formatFallbackMessages||e.formatFallbackMessages,e.postTranslation=t.postTranslation||e.postTranslation,e.warnHtmlInMessage=t.warnHtmlInMessage||e.warnHtmlInMessage,e.escapeParameterHtml=t.escapeParameterHtml||e.escapeParameterHtml,e.sync=t.sync||e.sync,e.__composer[Hn](t.pluralizationRules||e.pluralizationRules);const n=Ue(e.locale,{messages:t.messages,__i18n:t.__i18n});return Object.keys(n).forEach(r=>e.mergeLocaleMessage(r,n[r])),t.datetimeFormats&&Object.keys(t.datetimeFormats).forEach(r=>e.mergeDateTimeFormat(r,t.datetimeFormats[r])),t.numberFormats&&Object.keys(t.numberFormats).forEach(r=>e.mergeNumberFormat(r,t.numberFormats[r])),e}const Xn=f.makeSymbol("global-vue-i18n");function Sa(e={},t){const n=f.isBoolean(e.legacy)?e.legacy:!0,r=f.isBoolean(e.globalInjection)?e.globalInjection:!0,a=n?!!e.allowComposition:!0,o=new Map,[s,i]=Pa(e,n),c=f.makeSymbol("");function l(E){return o.get(E)||null}function d(E,v){o.set(E,v)}function h(E){o.delete(E)}{const E={get mode(){return n?"legacy":"composition"},get allowComposition(){return a},async install(v,...u){v.__VUE_I18N_SYMBOL__=c,v.provide(v.__VUE_I18N_SYMBOL__,E),!n&&r&&Ma(v,E.global),Ta(v,E,...u),n&&v.mixin(Oa(i,i.__composer,E));const m=v.unmount;v.unmount=()=>{E.dispose(),m()}},get global(){return i},dispose(){s.stop()},__instances:o,__getInstance:l,__setInstance:d,__deleteInstance:h};return E}}function $e(e={}){const t=N.getCurrentInstance();if(t==null)throw U(B.MUST_BE_CALL_SETUP_TOP);if(!t.isCE&&t.appContext.app!=null&&!t.appContext.app.__VUE_I18N_SYMBOL__)throw U(B.NOT_INSLALLED);const n=Ra(t),r=Aa(n),a=ya(t),o=Fa(e,a);if(n.mode==="legacy"&&!e.__useComponent){if(!n.allowComposition)throw U(B.NOT_AVAILABLE_IN_LEGACY_MODE);return Ca(t,o,r,e)}if(o==="global")return Gn(r,e,a),r;if(o==="parent"){let c=Na(n,t,e.__useComponent);return c==null&&(c=r),c}const s=n;let i=s.__getInstance(t);if(i==null){const c=f.assign({},e);"__i18n"in a&&(c.__i18n=a.__i18n),r&&(c.__root=r),i=Vt(c),xa(s,t),s.__setInstance(t,i)}return i}const La=e=>{if(!(_a in e))throw U(B.NOT_COMPATIBLE_LEGACY_VUE_I18N);return e};function Pa(e,t,n){const r=N.effectScope();{const a=t?r.run(()=>Lt(e)):r.run(()=>Vt(e));if(a==null)throw U(B.UNEXPECTED_ERROR);return[r,a]}}function Ra(e){{const t=N.inject(e.isCE?Xn:e.appContext.app.__VUE_I18N_SYMBOL__);if(!t)throw U(e.isCE?B.NOT_INSLALLED_WITH_PROVIDE:B.UNEXPECTED_ERROR);return t}}function Fa(e,t){return f.isEmptyObject(e)?"__i18n"in t?"local":"global":e.useScope?e.useScope:"local"}function Aa(e){return e.mode==="composition"?e.global:e.global.__composer}function Na(e,t,n=!1){let r=null;const a=t.root;let o=t.parent;for(;o!=null;){const s=e;if(e.mode==="composition")r=s.__getInstance(o);else{const i=s.__getInstance(o);i!=null&&(r=i.__composer,n&&r&&!r[qn]&&(r=null))}if(r!=null||a===o)break;o=o.parent}return r}function xa(e,t,n){N.onMounted(()=>{},t),N.onUnmounted(()=>{e.__deleteInstance(t)},t)}function Ca(e,t,n,r={}){const a=t==="local",o=N.shallowRef(null);if(a&&e.proxy&&!(e.proxy.$options.i18n||e.proxy.$options.__i18n))throw U(B.MUST_DEFINE_I18N_OPTION_IN_ALLOW_COMPOSITION);const s=f.isBoolean(r.inheritLocale)?r.inheritLocale:!0,i=N.ref(a&&s?n.locale.value:f.isString(r.locale)?r.locale:R.DEFAULT_LOCALE),c=N.ref(a&&s?n.fallbackLocale.value:f.isString(r.fallbackLocale)||f.isArray(r.fallbackLocale)||f.isPlainObject(r.fallbackLocale)||r.fallbackLocale===!1?r.fallbackLocale:i.value),l=N.ref(Ue(i.value,r)),d=N.ref(f.isPlainObject(r.datetimeFormats)?r.datetimeFormats:{[i.value]:{}}),h=N.ref(f.isPlainObject(r.numberFormats)?r.numberFormats:{[i.value]:{}}),E=a?n.missingWarn:f.isBoolean(r.missingWarn)||f.isRegExp(r.missingWarn)?r.missingWarn:!0,v=a?n.fallbackWarn:f.isBoolean(r.fallbackWarn)||f.isRegExp(r.fallbackWarn)?r.fallbackWarn:!0,u=a?n.fallbackRoot:f.isBoolean(r.fallbackRoot)?r.fallbackRoot:!0,m=!!r.fallbackFormat,k=f.isFunction(r.missing)?r.missing:null,O=f.isFunction(r.postTranslation)?r.postTranslation:null,P=a?n.warnHtmlMessage:f.isBoolean(r.warnHtmlMessage)?r.warnHtmlMessage:!0,F=!!r.escapeParameter,S=a?n.modifiers:f.isPlainObject(r.modifiers)?r.modifiers:{},T=r.pluralRules||a&&n.pluralRules;function j(){return[i.value,c.value,l.value,d.value,h.value]}const b=N.computed({get:()=>o.value?o.value.locale.value:i.value,set:w=>{o.value&&(o.value.locale.value=w),i.value=w}}),_=N.computed({get:()=>o.value?o.value.fallbackLocale.value:c.value,set:w=>{o.value&&(o.value.fallbackLocale.value=w),c.value=w}}),A=N.computed(()=>o.value?o.value.messages.value:l.value),I=N.computed(()=>d.value),se=N.computed(()=>h.value);function Ke(){return o.value?o.value.getPostTranslationHandler():O}function Xe(w){o.value&&o.value.setPostTranslationHandler(w)}function Qe(){return o.value?o.value.getMissingHandler():k}function Ze(w){o.value&&o.value.setMissingHandler(w)}function te(w){return j(),w()}function ne(...w){return o.value?te(()=>Reflect.apply(o.value.t,null,[...w])):te(()=>"")}function Le(...w){return o.value?Reflect.apply(o.value.rt,null,[...w]):""}function et(...w){return o.value?te(()=>Reflect.apply(o.value.d,null,[...w])):te(()=>"")}function tt(...w){return o.value?te(()=>Reflect.apply(o.value.n,null,[...w])):te(()=>"")}function nt(w){return o.value?o.value.tm(w):{}}function rt(w,M){return o.value?o.value.te(w,M):!1}function Zt(w){return o.value?o.value.getLocaleMessage(w):{}}function at(w,M){o.value&&(o.value.setLocaleMessage(w,M),l.value[w]=M)}function ot(w,M){o.value&&o.value.mergeLocaleMessage(w,M)}function st(w){return o.value?o.value.getDateTimeFormat(w):{}}function it(w,M){o.value&&(o.value.setDateTimeFormat(w,M),d.value[w]=M)}function lt(w,M){o.value&&o.value.mergeDateTimeFormat(w,M)}function ct(w){return o.value?o.value.getNumberFormat(w):{}}function ut(w,M){o.value&&(o.value.setNumberFormat(w,M),h.value[w]=M)}function ft(w,M){o.value&&o.value.mergeNumberFormat(w,M)}const Pe={get id(){return o.value?o.value.id:-1},locale:b,fallbackLocale:_,messages:A,datetimeFormats:I,numberFormats:se,get inheritLocale(){return o.value?o.value.inheritLocale:s},set inheritLocale(w){o.value&&(o.value.inheritLocale=w)},get availableLocales(){return o.value?o.value.availableLocales:Object.keys(l.value)},get modifiers(){return o.value?o.value.modifiers:S},get pluralRules(){return o.value?o.value.pluralRules:T},get isGlobal(){return o.value?o.value.isGlobal:!1},get missingWarn(){return o.value?o.value.missingWarn:E},set missingWarn(w){o.value&&(o.value.missingWarn=w)},get fallbackWarn(){return o.value?o.value.fallbackWarn:v},set fallbackWarn(w){o.value&&(o.value.missingWarn=w)},get fallbackRoot(){return o.value?o.value.fallbackRoot:u},set fallbackRoot(w){o.value&&(o.value.fallbackRoot=w)},get fallbackFormat(){return o.value?o.value.fallbackFormat:m},set fallbackFormat(w){o.value&&(o.value.fallbackFormat=w)},get warnHtmlMessage(){return o.value?o.value.warnHtmlMessage:P},set warnHtmlMessage(w){o.value&&(o.value.warnHtmlMessage=w)},get escapeParameter(){return o.value?o.value.escapeParameter:F},set escapeParameter(w){o.value&&(o.value.escapeParameter=w)},t:ne,getPostTranslationHandler:Ke,setPostTranslationHandler:Xe,getMissingHandler:Qe,setMissingHandler:Ze,rt:Le,d:et,n:tt,tm:nt,te:rt,getLocaleMessage:Zt,setLocaleMessage:at,mergeLocaleMessage:ot,getDateTimeFormat:st,setDateTimeFormat:it,mergeDateTimeFormat:lt,getNumberFormat:ct,setNumberFormat:ut,mergeNumberFormat:ft};function dt(w){w.locale.value=i.value,w.fallbackLocale.value=c.value,Object.keys(l.value).forEach(M=>{w.mergeLocaleMessage(M,l.value[M])}),Object.keys(d.value).forEach(M=>{w.mergeDateTimeFormat(M,d.value[M])}),Object.keys(h.value).forEach(M=>{w.mergeNumberFormat(M,h.value[M])}),w.escapeParameter=F,w.fallbackFormat=m,w.fallbackRoot=u,w.fallbackWarn=v,w.missingWarn=E,w.warnHtmlMessage=P}return N.onBeforeMount(()=>{if(e.proxy==null||e.proxy.$i18n==null)throw U(B.NOT_AVAILABLE_COMPOSITION_IN_LEGACY);const w=o.value=e.proxy.$i18n.__composer;t==="global"?(i.value=w.locale.value,c.value=w.fallbackLocale.value,l.value=w.messages.value,d.value=w.datetimeFormats.value,h.value=w.numberFormats.value):a&&dt(w)}),Pe}const Da=["locale","fallbackLocale","availableLocales"],Ia=["t","rt","d","n","tm"];function Ma(e,t){const n=Object.create(null);Da.forEach(r=>{const a=Object.getOwnPropertyDescriptor(t,r);if(!a)throw U(B.UNEXPECTED_ERROR);const o=N.isRef(a.value)?{get(){return a.value.value},set(s){a.value.value=s}}:{get(){return a.get&&a.get()}};Object.defineProperty(n,r,o)}),e.config.globalProperties.$i18n=n,Ia.forEach(r=>{const a=Object.getOwnPropertyDescriptor(t,r);if(!a||!a.value)throw U(B.UNEXPECTED_ERROR);Object.defineProperty(e.config.globalProperties,`$${r}`,a)})}R.registerMessageCompiler(R.compileToFunction);R.registerMessageResolver(R.resolveValue);R.registerLocaleFallbacker(R.fallbackWithLocaleChain);Q.DatetimeFormat=Ft;Q.I18nInjectionKey=Xn;Q.NumberFormat=Rt;Q.Translation=Pt;Q.VERSION=zn;Q.castToVueI18n=La;var ja=Q.createI18n=Sa,Ba=Q.useI18n=$e;Q.vTDirective=Kn;var Qn={exports:{}};/* NProgress, (c) 2013, 2014 Rico Sta. Cruz - http://ricostacruz.com/nprogress
 * @license MIT */(function(e,t){(function(n,r){e.exports=r()})(Yr,function(){var n={};n.version="0.2.0";var r=n.settings={minimum:.08,easing:"ease",positionUsing:"",speed:200,trickle:!0,trickleRate:.02,trickleSpeed:800,showSpinner:!0,barSelector:'[role="bar"]',spinnerSelector:'[role="spinner"]',parent:"body",template:'<div class="bar" role="bar"><div class="peg"></div></div><div class="spinner" role="spinner"><div class="spinner-icon"></div></div>'};n.configure=function(u){var m,k;for(m in u)k=u[m],k!==void 0&&u.hasOwnProperty(m)&&(r[m]=k);return this},n.status=null,n.set=function(u){var m=n.isStarted();u=a(u,r.minimum,1),n.status=u===1?null:u;var k=n.render(!m),O=k.querySelector(r.barSelector),P=r.speed,F=r.easing;return k.offsetWidth,i(function(S){r.positionUsing===""&&(r.positionUsing=n.getPositioningCSS()),c(O,s(u,P,F)),u===1?(c(k,{transition:"none",opacity:1}),k.offsetWidth,setTimeout(function(){c(k,{transition:"all "+P+"ms linear",opacity:0}),setTimeout(function(){n.remove(),S()},P)},P)):setTimeout(S,P)}),this},n.isStarted=function(){return typeof n.status=="number"},n.start=function(){n.status||n.set(0);var u=function(){setTimeout(function(){n.status&&(n.trickle(),u())},r.trickleSpeed)};return r.trickle&&u(),this},n.done=function(u){return!u&&!n.status?this:n.inc(.3+.5*Math.random()).set(1)},n.inc=function(u){var m=n.status;return m?(typeof u!="number"&&(u=(1-m)*a(Math.random()*m,.1,.95)),m=a(m+u,0,.994),n.set(m)):n.start()},n.trickle=function(){return n.inc(Math.random()*r.trickleRate)},function(){var u=0,m=0;n.promise=function(k){return!k||k.state()==="resolved"?this:(m===0&&n.start(),u++,m++,k.always(function(){m--,m===0?(u=0,n.done()):n.set((u-m)/u)}),this)}}(),n.render=function(u){if(n.isRendered())return document.getElementById("nprogress");d(document.documentElement,"nprogress-busy");var m=document.createElement("div");m.id="nprogress",m.innerHTML=r.template;var k=m.querySelector(r.barSelector),O=u?"-100":o(n.status||0),P=document.querySelector(r.parent),F;return c(k,{transition:"all 0 linear",transform:"translate3d("+O+"%,0,0)"}),r.showSpinner||(F=m.querySelector(r.spinnerSelector),F&&v(F)),P!=document.body&&d(P,"nprogress-custom-parent"),P.appendChild(m),m},n.remove=function(){h(document.documentElement,"nprogress-busy"),h(document.querySelector(r.parent),"nprogress-custom-parent");var u=document.getElementById("nprogress");u&&v(u)},n.isRendered=function(){return!!document.getElementById("nprogress")},n.getPositioningCSS=function(){var u=document.body.style,m="WebkitTransform"in u?"Webkit":"MozTransform"in u?"Moz":"msTransform"in u?"ms":"OTransform"in u?"O":"";return m+"Perspective"in u?"translate3d":m+"Transform"in u?"translate":"margin"};function a(u,m,k){return u<m?m:u>k?k:u}function o(u){return(-1+u)*100}function s(u,m,k){var O;return r.positionUsing==="translate3d"?O={transform:"translate3d("+o(u)+"%,0,0)"}:r.positionUsing==="translate"?O={transform:"translate("+o(u)+"%,0)"}:O={"margin-left":o(u)+"%"},O.transition="all "+m+"ms "+k,O}var i=function(){var u=[];function m(){var k=u.shift();k&&k(m)}return function(k){u.push(k),u.length==1&&m()}}(),c=function(){var u=["Webkit","O","Moz","ms"],m={};function k(S){return S.replace(/^-ms-/,"ms-").replace(/-([\da-z])/gi,function(T,j){return j.toUpperCase()})}function O(S){var T=document.body.style;if(S in T)return S;for(var j=u.length,b=S.charAt(0).toUpperCase()+S.slice(1),_;j--;)if(_=u[j]+b,_ in T)return _;return S}function P(S){return S=k(S),m[S]||(m[S]=O(S))}function F(S,T,j){T=P(T),S.style[T]=j}return function(S,T){var j=arguments,b,_;if(j.length==2)for(b in T)_=T[b],_!==void 0&&T.hasOwnProperty(b)&&F(S,b,_);else F(S,j[1],j[2])}}();function l(u,m){var k=typeof u=="string"?u:E(u);return k.indexOf(" "+m+" ")>=0}function d(u,m){var k=E(u),O=k+m;l(k,m)||(u.className=O.substring(1))}function h(u,m){var k=E(u),O;l(u,m)&&(O=k.replace(" "+m+" "," "),u.className=O.substring(1,O.length-1))}function E(u){return(" "+(u.className||"")+" ").replace(/\s+/gi," ")}function v(u){u&&u.parentNode&&u.parentNode.removeChild(u)}return n})})(Qn);var Ua=Qn.exports;const At=Bt(Ua);const $a="modulepreload",Va=function(e){return"/admin/"+e},cn={},x=function(t,n,r){if(!n||n.length===0)return t();const a=document.getElementsByTagName("link");return Promise.all(n.map(o=>{if(o=Va(o),o in cn)return;cn[o]=!0;const s=o.endsWith(".css"),i=s?'[rel="stylesheet"]':"";if(!!r)for(let d=a.length-1;d>=0;d--){const h=a[d];if(h.href===o&&(!s||h.rel==="stylesheet"))return}else if(document.querySelector(`link[href="${o}"]${i}`))return;const l=document.createElement("link");if(l.rel=s?"stylesheet":$a,s||(l.as="script",l.crossOrigin=""),l.href=o,document.head.appendChild(l),s)return new Promise((d,h)=>{l.addEventListener("load",d),l.addEventListener("error",()=>h(new Error(`Unable to preload CSS for ${o}`)))})})).then(()=>t())},ye=e=>`pagesTitle.${e}`,Zn=[{path:"/",redirect:"/admin/login"},{path:"/admin/login",name:"adminLogin",component:()=>x(()=>import("./login-03f9aa99.js"),["assets/login-03f9aa99.js","assets/vue-9f0739d1.js","assets/avatar-75c8f563.js","assets/useDark-3f3e4e6b.js","assets/validate-eddfbf9e.js"]),meta:{title:ye("adminLogin")}},{path:"/:path(.*)*",redirect:"/404"},{path:"/404",name:"notFound",component:()=>x(()=>import("./404-fb389847.js"),["assets/404-fb389847.js","assets/vue-9f0739d1.js"]),meta:{title:ye("notFound")}},{path:"/admin:path(.*)*",redirect:e=>({name:"adminMainLoading",params:{to:JSON.stringify({path:e.path,query:e.query})}})},{path:"/401",name:"noPower",component:()=>x(()=>import("./401-098b8547.js"),["assets/401-098b8547.js","assets/vue-9f0739d1.js"]),meta:{title:ye("noPower")}}],Nt={path:"/admin",name:"admin",component:()=>x(()=>import("./index-562b60d4.js"),["assets/index-562b60d4.js","assets/vue-9f0739d1.js","assets/router-e3edf306.js","assets/storage-19836658.js","assets/layout-9c3294eb.js","assets/index-1d626fdc.js","assets/index-d8b6d591.js","assets/useDark-3f3e4e6b.js","assets/index-f8da5656.js"]),redirect:"/admin/loading",meta:{title:ye("admin")},children:[{path:"loading/:to?",name:"adminMainLoading",component:()=>x(()=>import("./loading-fab29006.js"),["assets/loading-fab29006.js","assets/router-e3edf306.js","assets/vue-9f0739d1.js"]),meta:{title:ye("Loading")}}]};Zn.push(Nt);const er={show:()=>{const e=document.body,t=document.createElement("div");t.className="block-loading",t.innerHTML=`
            <div class="block-loading-box">
                <div class="block-loading-box-warp">
                    <div class="block-loading-box-item"></div>
                    <div class="block-loading-box-item"></div>
                    <div class="block-loading-box-item"></div>
                    <div class="block-loading-box-item"></div>
                    <div class="block-loading-box-item"></div>
                    <div class="block-loading-box-item"></div>
                    <div class="block-loading-box-item"></div>
                    <div class="block-loading-box-item"></div>
                    <div class="block-loading-box-item"></div>
                </div>
            </div>
        `,e.insertBefore(t,e.childNodes[0])},hide:()=>{X(()=>{setTimeout(()=>{var t;const e=document.querySelector(".block-loading");e&&((t=e.parentNode)==null||t.removeChild(e))},1e3)})}},un={"/":["./frontend/${lang}/index.ts"],"/admin/moduleStore":["./backend/${lang}/module.ts"],"/admin/user/rule":["./backend/${lang}/auth/rule.ts"],"/admin/user/scoreLog":["./backend/${lang}/user/moneyLog.ts"],"/admin/crud/crud":["./backend/${lang}/crud/log.ts","./backend/${lang}/crud/state.ts"]},za={noPowerTip:"这不是你想要的，但我们是认真的。我只是想用一种特殊的方式告诉你，你无权访问此页面，或者该文件无效。您可以联系网站管理员以更快地解决问题，或返回网站首页浏览其他页面。"},Wa=Object.freeze(Object.defineProperty({__proto__:null,default:za},Symbol.toStringTag,{value:"Module"})),Ha={"problems tip":"你的网页遇到了一些问题，系统正在优化和上报故障信息，我们在未来将改善和减少这种情况的发生.","We will automatically return to the previous page when we are finished":"我们将在完成后自动返回到上一页。","Return to home page":"返回首页","Back to previous page":"返回上一页"},qa=Object.freeze(Object.defineProperty({__proto__:null,default:Ha},Symbol.toStringTag,{value:"Module"})),Ga={"Operation successful":"操作成功","Automatic cancellation due to duplicate request:":"因为请求重复被自动取消：","Interface redirected!":"接口重定向了！","Incorrect parameter!":"参数不正确！","You do not have permission to operate!":"您没有权限操作！","Error requesting address:":"请求地址出错:","Request timed out!":"请求超时！","The same data already exists in the system!":"系统已存在相同数据！","Server internal error!":"服务器内部错误！","Service not implemented!":"服务未实现！","Gateway error!":"网关错误！","Service unavailable!":"服务不可用！","The service is temporarily unavailable Please try again later!":"服务暂时无法访问，请稍后再试！","HTTP version is not supported!":"HTTP版本不受支持！","Abnormal problem, please contact the website administrator!":"异常问题，请联系网站管理员！","Network request timeout!":"网络请求超时！","Server exception!":"服务端异常！","You are disconnected!":"您断网了！"},Ja=Object.freeze(Object.defineProperty({__proto__:null,default:Ga},Symbol.toStringTag,{value:"Module"})),Ya={home:"首页",admin:"后台",adminLogin:"登录",notFound:"页面找不到了",noPower:"无访问权限",noTitle:"无标题",Loading:"Loading...",User:"会员中心",userLogin:"会员登录"},Ka=Object.freeze(Object.defineProperty({__proto__:null,default:Ya},Symbol.toStringTag,{value:"Module"})),Xa={"The moving position is beyond the movable range!":"移动位置超出了可移动范围！","Navigation failed, the menu type is unrecognized!":"导航失败，菜单类型无法识别！","Navigation failed, navigation guard intercepted!":"导航失败，导航守卫拦截！","Navigation failed, it is at the navigation target position!":"导航失败，已在导航目标位置！","Navigation failed, invalid route!":"导航失败，路由无效！",Loading:"加载中...",Reload:"重新加载",comma:"，","welcome back":"欢迎回来！","Late at night, pay attention to your body!":"夜深了，注意身体哦！","good morning!":"早上好！","Good morning!":"上午好！","Good noon!":"中午好！","good afternoon":"下午好！","Good evening":"晚上好！","Hello!":"您好！",open:"开启",close:"关闭","Clean up system cache":"清理系统缓存","Clean up browser cache":"清理浏览器缓存","Clean up all cache":"一键清理所有","The data of the uploaded file is incomplete!":"上传文件的资料不完整！","The type of uploaded file is not allowed!":"上传文件的类型不被允许！","The size of the uploaded file exceeds the allowed range!":"上传文件的大小超出允许范围！","Please install editor":"请先于模块市场安装富文本编辑器。",mobile:"手机号","Id number":"身份证号",account:"账户名",password:"密码","variable name":"变量名",email:"邮箱地址",date:"日期",number:"数字",float:"浮点数",integer:"整数",time:"时间",file:"文件",array:"数组",switch:"开关",year:"年份",image:"图片",select:"下拉框",string:"字符串",radio:"单选框",checkbox:"复选框","rich Text":"富文本","multi image":"多图",textarea:"多行文本框","time date":"时间日期","remote select":"远程下拉","city select":"城市选择","icon select":"图标选择","color picker":"颜色选择器",color:"颜色",choice:"选择",Icon:"图标","Local icon title":"本地图标:/src/assets/icons中的.svg","Please select an icon":"请选择图标","Ali iconcont Icon":"阿里 Iconfont 图标","Select File":"选择文件","Original name":"原始名称","You can also select":"还可以选择",items:"项",Breakdown:"细目",size:"大小",type:"类型",preview:"预览","Upload (Reference) times":"上传(引用)次数","Last upload time":"最后上传时间","One attribute per line without quotation marks(formitem)":"FormItem 的扩展属性，一行一个，无需引号，比如：class=config-item","Extended properties of Input, one line without quotation marks, such as: size=large":"Input 的扩展属性，一行一个，无需引号，比如：size=large","One line at a time, without quotation marks, for example: key1=value1":"一行一个，无需引号，比如：key1=value1",Var:"变量",Name:"名",Title:"标题",Tip:"提示信息",Rule:"验证规则",Extend:"扩展属性",Dict:"字典数据",ArrayKey:"键名",ArrayValue:"键值","No data":"无数据"},Qa=Object.freeze(Object.defineProperty({__proto__:null,default:Xa},Symbol.toStringTag,{value:"Module"})),Za={"Captcha loading failed, please click refresh button":"验证码加载失败，请点击刷新按钮","The correct area is not clicked, please try again!":"未点中正确区域，请重试！","Verification is successful!":"验证成功！","Please click":"请依次点击","Please enter the correct mobile number":"请输入正确的手机号","Please enter the correct account":"要求3到15位，字母开头且只含字母、数字、下划线","Please enter the correct password":`密码要求6到32位，不能包含 & < > " '`,"Please enter the correct name":"请输入正确的名称","Content cannot be empty":"内容不能为空","Floating point number":"浮点数",required:"必填","editor required":"富文本必填","Please enter the correct ID number":"请输入正确的身份证号码"},eo=Object.freeze(Object.defineProperty({__proto__:null,default:Za},Symbol.toStringTag,{value:"Module"})),to={noPowerTip:"It's not what you want, but we're serious. I want to tell you in a special way that you don't have permission to access this page or the file is invalid. You can contact the website administrator to solve the problem faster or go back home page to view another page."},no=Object.freeze(Object.defineProperty({__proto__:null,default:to},Symbol.toStringTag,{value:"Module"})),ro={"problems tip":"Your website has encountered some problems. The system is optimizing and reporting fault information. We will improve and reduce this situation in the future.","We will automatically return to the previous page when we are finished":"Auto return to previous page when finished.","Return to home page":"Back to Home","Back to previous page":"Back to previous page"},ao=Object.freeze(Object.defineProperty({__proto__:null,default:ro},Symbol.toStringTag,{value:"Module"})),oo={"Operation successful":"Operate successful","Automatic cancellation due to duplicate request:":"Automatic cancellation due to duplicate requests:","Interface redirected!":"Interface redirected!","Incorrect parameter!":"Incorrect parameter!","You do not have permission to operate!":"You have no permission to operate!","Error requesting address:":"Error requesting address:","Request timed out!":"Request timeout!","The same data already exists in the system!":"The same data already exists on the system!","Server internal error!":"Internal server error!","Service not implemented!":"Service unrealized!","Gateway error!":"Gateway error!","Service unavailable!":"Service unavailable!","The service is temporarily unavailable Please try again later!":"The service is temporarily unavailable, please try again later!","HTTP version is not supported!":"HTTP version is not Unsupported!","Abnormal problem, please contact the website administrator!":"Abnormal problems, please contact the website administrator!","Network request timeout!":"Network request timeout!","Server exception!":"Server-side exceptions!","You are disconnected!":"You are disconnected!"},so=Object.freeze(Object.defineProperty({__proto__:null,default:oo},Symbol.toStringTag,{value:"Module"})),io={home:"Home",admin:"Admin",adminLogin:"Login",notFound:"Page not found",noPower:"No access permission",noTitle:"No title",Loading:"Loading...",User:"Member Center",userLogin:"Menber Login"},lo=Object.freeze(Object.defineProperty({__proto__:null,default:io},Symbol.toStringTag,{value:"Module"})),co={"The moving position is beyond the movable range!":"The movement position is beyond the removable range!","Navigation failed, the menu type is unrecognized!":"Navigation failed, menu type not recognized!","Navigation failed, navigation guard intercepted!":"Navigation failed, Navigation Guard interception!","Navigation failed, it is at the navigation target position!":"Navigation failed, it is already at the navigation the position!","Navigation failed, invalid route!":"Navigation failed, invalid route!",Loading:"Loading...",Reload:"Reload",comma:",","welcome back":"Welcome back!","Late at night, pay attention to your body!":"It is late at night. Please tack care of your body!","good morning!":"Good morning!","Good morning!":"Good morning!","Good noon!":"Good noon!","good afternoon":"Good afternoon.","Good evening":"Good evening","Hello!":"Hello!",open:"Open",close:"Close","Clean up system cache":"Clean up the system cache","Clean up browser cache":"Clean up browser cache","Clean up all cache":"Clean up all cache","The data of the uploaded file is incomplete!":"The data of the uploaded file is incomplete!","The type of uploaded file is not allowed!":"The type of uploaded file is not allowed!","The size of the uploaded file exceeds the allowed range!":"The size of the uploaded file exceeds the allowed range!","Please install editor":"Please install editor",mobile:"Mobile Number","Id number":"Id Number",account:"Account name",password:"password","variable name":"Variable Name",email:"Email address",date:"Date",number:"Number",float:"Float",integer:"Integer",time:"Time",file:"File",array:"Array",switch:"Switch",year:"Year",image:"Image",select:"Select",string:"String",radio:"Radio",checkbox:"checkbox","rich Text":"Rich Text","multi image":"Multi image",textarea:"Textarea","time date":"Time Date","remote select":"Remote Select","city select":"City select","icon select":"Icon select","color picker":"color picker",color:"color",choice:" Choice",Icon:"Icon","Local icon title":"Local icon:/src/assets/icons Inside.svg","Please select an icon":"Please select an icon","Ali iconcont Icon":"Ali Iconfont Icon","Select File":"Select File","Original name":"Original name","You can also select":"You can also select",items:"items",Breakdown:"Detailed catalogue",size:"Size",type:"Type",preview:"Preview","Upload (Reference) times":"Upload (Reference) times","Last upload time":"Last upload time","One attribute per line without quotation marks(formitem)":"Extensions to FormItem, One attribute per line, no quotation marks required, such as: class=config-item","Extended properties of Input, one line without quotation marks, such as: size=large":"Extended properties of Input, one line without quotation marks, such as: size=large","One line at a time, without quotation marks, for example: key1=value1":"One per line, no quotation marks required, such as: key1=value1",Var:"Var ",Name:"Name",Title:"Title",Tip:"Tip",Rule:"Rule",Extend:"Extend",Dict:"Dict",ArrayKey:"Key",ArrayValue:"Value","No data":"No data"},uo=Object.freeze(Object.defineProperty({__proto__:null,default:co},Symbol.toStringTag,{value:"Module"})),fo={"Captcha loading failed, please click refresh button":"Captcha loading failed, please click refresh button","The correct area is not clicked, please try again!":"The correct area is not clicked, please try again!","Verification is successful!":"Verification is successful!","Please click":"Please click","Please enter the correct mobile number":"Please enter the correct mobile number","Please enter the correct account":"The account requires 3 to 15 characters and contains a-z A-Z 0-9 _","Please enter the correct password":`The password requires 6 to 32 characters and cannot contains & < > " '`,"Please enter the correct name":"Please enter the correct name","Content cannot be empty":"The content cannot be blank","Floating point number":" Floating number",required:"Required","editor required":"editor Required","Please enter the correct ID number":"Please enter the correct ID number"},mo=Object.freeze(Object.defineProperty({__proto__:null,default:fo},Symbol.toStringTag,{value:"Module"})),go=(e,t)=>{const n=e[t];return n?typeof n=="function"?n():Promise.resolve(n):new Promise((r,a)=>{(typeof queueMicrotask=="function"?queueMicrotask:setTimeout)(a.bind(null,new Error("Unknown variable dynamic import: "+t)))})},po="adminInfo",ho="storeConfig_v2",bo="storeTabViewConfig",_o="storeTerminal",Nl="workingTime",xl="beforeResizeLayout",Cl="ba_account",Te=ke("config",()=>{const e=Me({showDrawer:!1,shrink:!1,layoutMode:"Default",mainAnimation:"slide-right",isDark:!1,menuBackground:["#ffffff","#1d1e1f"],menuColor:["#303133","#CFD3DC"],menuActiveBackground:["#ffffff","#1d1e1f"],menuActiveColor:["#409eff","#3375b9"],menuTopBarBackground:["#fcfcfc","#1d1e1f"],menuWidth:260,menuDefaultIcon:"fa fa-circle-o",menuCollapse:!1,menuUniqueOpened:!1,menuShowTopBar:!0,headerBarTabColor:["#000000","#CFD3DC"],headerBarTabActiveBackground:["#ffffff","#1d1e1f"],headerBarTabActiveColor:["#000000","#409EFF"],headerBarBackground:["#ffffff","#1d1e1f"],headerBarHoverBackground:["#f5f5f5","#18222c"]}),t=Me({defaultLang:"zh-cn",fallbackLang:"zh-cn",langArray:[{name:"zh-cn",value:"中文简体"},{name:"en",value:"English"}]});function n(){return e.shrink?e.menuCollapse?"0px":e.menuWidth+"px":e.menuCollapse?"64px":e.menuWidth+"px"}function r(c){t.defaultLang=c}function a(c=e.layoutMode){const l=e.isDark?{idx:1,color:"#1d1e1f",newColor:"#141414"}:{idx:0,color:"#ffffff",newColor:"#f5f5f5"};c=="Classic"&&e.headerBarBackground[l.idx]==l.color&&e.headerBarTabActiveBackground[l.idx]==l.color?e.headerBarTabActiveBackground[l.idx]=l.newColor:c=="Default"&&e.headerBarBackground[l.idx]==l.color&&e.headerBarTabActiveBackground[l.idx]==l.newColor&&(e.headerBarTabActiveBackground[l.idx]=l.color)}function o(c){e.layoutMode=c,a(c)}return{layout:e,lang:t,menuWidth:n,setLang:r,setLayoutMode:o,setLayout:(c,l)=>{e[c]=l},getColorVal:function(c){const l=e[c];return e.isDark?l[1]:l[0]},onSetLayoutColor:a}},{persist:{key:ho}});var tr={};(function(e){Object.defineProperty(e,"__esModule",{value:!0});var t={name:"zh-cn",el:{colorpicker:{confirm:"确定",clear:"清空"},datepicker:{now:"此刻",today:"今天",cancel:"取消",clear:"清空",confirm:"确定",selectDate:"选择日期",selectTime:"选择时间",startDate:"开始日期",startTime:"开始时间",endDate:"结束日期",endTime:"结束时间",prevYear:"前一年",nextYear:"后一年",prevMonth:"上个月",nextMonth:"下个月",year:"年",month1:"1 月",month2:"2 月",month3:"3 月",month4:"4 月",month5:"5 月",month6:"6 月",month7:"7 月",month8:"8 月",month9:"9 月",month10:"10 月",month11:"11 月",month12:"12 月",weeks:{sun:"日",mon:"一",tue:"二",wed:"三",thu:"四",fri:"五",sat:"六"},months:{jan:"一月",feb:"二月",mar:"三月",apr:"四月",may:"五月",jun:"六月",jul:"七月",aug:"八月",sep:"九月",oct:"十月",nov:"十一月",dec:"十二月"}},select:{loading:"加载中",noMatch:"无匹配数据",noData:"无数据",placeholder:"请选择"},cascader:{noMatch:"无匹配数据",loading:"加载中",placeholder:"请选择",noData:"暂无数据"},pagination:{goto:"前往",pagesize:"条/页",total:"共 {total} 条",pageClassifier:"页",page:"页",prev:"上一页",next:"下一页",currentPage:"第 {pager} 页",prevPages:"向前 {pager} 页",nextPages:"向后 {pager} 页",deprecationWarning:"你使用了一些已被废弃的用法，请参考 el-pagination 的官方文档"},messagebox:{title:"提示",confirm:"确定",cancel:"取消",error:"输入的数据不合法!"},upload:{deleteTip:"按 delete 键可删除",delete:"删除",preview:"查看图片",continue:"继续上传"},table:{emptyText:"暂无数据",confirmFilter:"筛选",resetFilter:"重置",clearFilter:"全部",sumText:"合计"},tree:{emptyText:"暂无数据"},transfer:{noMatch:"无匹配数据",noData:"无数据",titles:["列表 1","列表 2"],filterPlaceholder:"请输入搜索内容",noCheckedFormat:"共 {total} 项",hasCheckedFormat:"已选 {checked}/{total} 项"},image:{error:"加载失败"},pageHeader:{title:"返回"},popconfirm:{confirmButtonText:"确定",cancelButtonText:"取消"}}};e.default=t})(tr);const yo=Bt(tr);var nr={};(function(e){Object.defineProperty(e,"__esModule",{value:!0});var t={name:"en",el:{colorpicker:{confirm:"OK",clear:"Clear",defaultLabel:"color picker",description:"current color is {color}. press enter to select a new color."},datepicker:{now:"Now",today:"Today",cancel:"Cancel",clear:"Clear",confirm:"OK",dateTablePrompt:"Use the arrow keys and enter to select the day of the month",monthTablePrompt:"Use the arrow keys and enter to select the month",yearTablePrompt:"Use the arrow keys and enter to select the year",selectedDate:"Selected date",selectDate:"Select date",selectTime:"Select time",startDate:"Start Date",startTime:"Start Time",endDate:"End Date",endTime:"End Time",prevYear:"Previous Year",nextYear:"Next Year",prevMonth:"Previous Month",nextMonth:"Next Month",year:"",month1:"January",month2:"February",month3:"March",month4:"April",month5:"May",month6:"June",month7:"July",month8:"August",month9:"September",month10:"October",month11:"November",month12:"December",week:"week",weeks:{sun:"Sun",mon:"Mon",tue:"Tue",wed:"Wed",thu:"Thu",fri:"Fri",sat:"Sat"},weeksFull:{sun:"Sunday",mon:"Monday",tue:"Tuesday",wed:"Wednesday",thu:"Thursday",fri:"Friday",sat:"Saturday"},months:{jan:"Jan",feb:"Feb",mar:"Mar",apr:"Apr",may:"May",jun:"Jun",jul:"Jul",aug:"Aug",sep:"Sep",oct:"Oct",nov:"Nov",dec:"Dec"}},inputNumber:{decrease:"decrease number",increase:"increase number"},select:{loading:"Loading",noMatch:"No matching data",noData:"No data",placeholder:"Select"},dropdown:{toggleDropdown:"Toggle Dropdown"},cascader:{noMatch:"No matching data",loading:"Loading",placeholder:"Select",noData:"No data"},pagination:{goto:"Go to",pagesize:"/page",total:"Total {total}",pageClassifier:"",page:"Page",prev:"Go to previous page",next:"Go to next page",currentPage:"page {pager}",prevPages:"Previous {pager} pages",nextPages:"Next {pager} pages",deprecationWarning:"Deprecated usages detected, please refer to the el-pagination documentation for more details"},dialog:{close:"Close this dialog"},drawer:{close:"Close this dialog"},messagebox:{title:"Message",confirm:"OK",cancel:"Cancel",error:"Illegal input",close:"Close this dialog"},upload:{deleteTip:"press delete to remove",delete:"Delete",preview:"Preview",continue:"Continue"},slider:{defaultLabel:"slider between {min} and {max}",defaultRangeStartLabel:"pick start value",defaultRangeEndLabel:"pick end value"},table:{emptyText:"No Data",confirmFilter:"Confirm",resetFilter:"Reset",clearFilter:"All",sumText:"Sum"},tree:{emptyText:"No Data"},transfer:{noMatch:"No matching data",noData:"No data",titles:["List 1","List 2"],filterPlaceholder:"Enter keyword",noCheckedFormat:"{total} items",hasCheckedFormat:"{checked}/{total} checked"},image:{error:"FAILED"},pageHeader:{title:"Back"},popconfirm:{confirmButtonText:"Yes",cancelButtonText:"No"}}};e.default=t})(nr);const vo=Bt(nr);let L;const gt={"zh-cn":[yo],en:[vo]};async function wo(e){const t=Te(),n=t.lang.defaultLang,a=(await go(Object.assign({"./globs-en.ts":()=>x(()=>import("./globs-en-b1201d6b.js"),[]),"./globs-zh-cn.ts":()=>x(()=>import("./globs-zh-cn-5499e13c.js"),[])}),`./globs-${n}.ts`)).default??{};n=="zh-cn"?window.loadLangHandle={...Object.assign({"./backend/zh-cn/auth/admin.ts":()=>x(()=>import("./admin-f063a068.js"),[]),"./backend/zh-cn/auth/adminLog.ts":()=>x(()=>import("./adminLog-99dee120.js"),[]),"./backend/zh-cn/auth/group.ts":()=>x(()=>import("./group-90ae3cb1.js"),[]),"./backend/zh-cn/auth/rule.ts":()=>x(()=>import("./rule-d2f56f14.js"),[]),"./backend/zh-cn/crud/crud.ts":()=>x(()=>import("./crud-ff4c3213.js"),[]),"./backend/zh-cn/crud/log.ts":()=>x(()=>import("./log-ff3fa817.js"),[]),"./backend/zh-cn/crud/state.ts":()=>x(()=>import("./state-146fa800.js"),[]),"./backend/zh-cn/dashboard.ts":()=>x(()=>import("./dashboard-0bd54af4.js"),[]),"./backend/zh-cn/fish/wallet.ts":()=>x(()=>import("./wallet-1ede01b4.js"),[]),"./backend/zh-cn/login.ts":()=>x(()=>import("./login-915022cb.js"),[]),"./backend/zh-cn/routine/adminInfo.ts":()=>x(()=>import("./adminInfo-cd060f4e.js"),[]),"./backend/zh-cn/routine/attachment.ts":()=>x(()=>import("./attachment-9340cffd.js"),[]),"./backend/zh-cn/routine/config.ts":()=>x(()=>import("./config-e41052c9.js"),[])}),...Object.assign({}),...Object.assign({"./backend/zh-cn.ts":()=>x(()=>import("./zh-cn-9a0b6f0d.js"),[])}),...Object.assign({})}:window.loadLangHandle={...Object.assign({"./backend/en/auth/admin.ts":()=>x(()=>import("./admin-0fe9dec0.js"),[]),"./backend/en/auth/adminLog.ts":()=>x(()=>import("./adminLog-4cc902fb.js"),[]),"./backend/en/auth/group.ts":()=>x(()=>import("./group-e3df291b.js"),[]),"./backend/en/auth/rule.ts":()=>x(()=>import("./rule-f241d1fe.js"),[]),"./backend/en/crud/crud.ts":()=>x(()=>import("./crud-a364cc22.js"),[]),"./backend/en/crud/log.ts":()=>x(()=>import("./log-45b5d4bc.js"),[]),"./backend/en/crud/state.ts":()=>x(()=>import("./state-8bad3424.js"),[]),"./backend/en/dashboard.ts":()=>x(()=>import("./dashboard-cc92587d.js"),[]),"./backend/en/fish/wallet.ts":()=>x(()=>import("./wallet-d229e878.js"),[]),"./backend/en/login.ts":()=>x(()=>import("./login-c13a8b29.js"),[]),"./backend/en/routine/adminInfo.ts":()=>x(()=>import("./adminInfo-7e4b4bc8.js"),[]),"./backend/en/routine/attachment.ts":()=>x(()=>import("./attachment-4d39873b.js"),[]),"./backend/en/routine/config.ts":()=>x(()=>import("./config-34462342.js"),[])}),...Object.assign({}),...Object.assign({"./backend/en.ts":()=>x(()=>import("./en-19abc9b5.js"),[])}),...Object.assign({})},n=="zh-cn"?gt[n].push(fn(Object.assign({"./common/zh-cn/401.ts":Wa,"./common/zh-cn/404.ts":qa,"./common/zh-cn/axios.ts":Ja,"./common/zh-cn/pagesTitle.ts":Ka,"./common/zh-cn/utils.ts":Qa,"./common/zh-cn/validate.ts":eo}),n)):n=="en"&&gt[n].push(fn(Object.assign({"./common/en/401.ts":no,"./common/en/404.ts":ao,"./common/en/axios.ts":so,"./common/en/pagesTitle.ts":lo,"./common/en/utils.ts":uo,"./common/en/validate.ts":mo}),n));const o={[n]:{...a}};return Object.assign(o[n],...gt[n]),L=ja({locale:n,legacy:!1,globalInjection:!0,fallbackLocale:t.lang.fallbackLang,messages:o}),e.use(L),L}function fn(e,t){let n={};t="/"+t;for(const r in e)if(e[r].default){const a=r.slice(r.lastIndexOf(t)+(t.length+1),r.lastIndexOf("."));a.indexOf("/")>0?n=rr(n,e[r].default,a):n[a]=e[r].default}return n}function Eo(e,t=""){if(vt(e))return;if(!t)return L.global.mergeLocaleMessage(L.global.locale.value,e);let n={};t.indexOf("/")>0?n=rr(n,e,t):n[t]=e,L.global.mergeLocaleMessage(L.global.locale.value,n)}function rr(e,t,n){const r=n.split("/");let a={};for(let o=r.length-1;o>=0;o--)o==r.length-1?a={[r[o]]:t}:a={[r[o]]:a};return ar(e,a)}function ar(e,t){for(const n in t)typeof e[n]>"u"?e[n]=t[n]:typeof e[n]=="object"&&(e[n]=ar(e[n],t[n]));return e}function Dl(e){Te().setLang(e),location.reload()}const q=Kr({history:Xr(),routes:Zn});q.beforeEach((e,t,n)=>{At.configure({showSpinner:!1}),At.start(),window.existLoading||(er.show(),window.existLoading=!0);let r=[];const a=Te();e.path in un&&r.push(...un[e.path]);let o="";if(ue(e.fullPath)){o="./backend/"+a.lang.defaultLang;const i=e.path.slice(e.path.indexOf(Nt.path)+Nt.path.length);i&&r.push(o+i+".ts")}e.name&&r.push(o+"/"+e.name.toString()+".ts"),window.loadLangHandle.publicMessageLoaded||(window.loadLangHandle.publicMessageLoaded=[]);const s=o+".ts";window.loadLangHandle.publicMessageLoaded.includes(s)||(r.push(s),window.loadLangHandle.publicMessageLoaded.push(s)),r=Qr(r);for(const i in r)r[i]=r[i].replaceAll("${lang}",a.lang.defaultLang),r[i]in window.loadLangHandle&&window.loadLangHandle[r[i]]().then(c=>{const l=r[i].slice(r[i].lastIndexOf(o)+(o.length+1),r[i].lastIndexOf("."));Eo(c.default,l)});n()});q.afterEach(()=>{window.existLoading&&er.hide(),At.done()});const ko=["href"],To=Ut({__name:"index",props:{name:{default:""},size:{default:"18px"},color:{default:"#000000"}},setup(e){const t=e,n=`${t.size.replace("px","")}px`,r=Y(()=>`#${t.name}`),a=Y(()=>({color:t.color,fontSize:n})),o=Y(()=>Cr(t.name)),s=Y(()=>({width:n,height:n,mask:`url(${t.name}) no-repeat 50% 50%`,"-webkit-mask":`url(${t.name}) no-repeat 50% 50%`}));return(i,c)=>o.value?(wt(),en("div",{key:0,style:tn(s.value),class:"url-svg svg-icon icon"},null,4)):(wt(),en("svg",{key:1,class:"svg-icon icon",style:tn(a.value)},[Zr("use",{href:r.value},null,8,ko)],4))}});const Oo=(e,t)=>{const n=e.__vccOpts||e;for(const[r,a]of t)n[r]=a;return n},So=Oo(To,[["__scopeId","data-v-bd7f11f7"]]),Lo=Ut({name:"Icon",props:{name:{type:String,required:!0},size:{type:String,default:"18px"},color:{type:String,default:"#000000"}},setup(e){const t=Y(()=>{const{size:n,color:r}=e;return{fontSize:`${n.replace("px","")}px`,color:r}});return e.name.indexOf("el-icon-")===0?()=>be("el-icon",{class:"icon el-icon",style:t.value},[be(Et(e.name))]):e.name.indexOf("local-")===0||Cr(e.name)?()=>be(So,{name:e.name,size:e.size,color:e.color}):()=>be("i",{class:[e.name,"icon"],style:t.value})}}),Po=ke("navTabs",()=>{const e=Me({activeIndex:0,activeRoute:null,tabsView:[],tabFullScreen:!1,tabsViewRoutes:[],authNode:new Map});function t(l){if(l.meta.addtab){for(const d in e.tabsView)if(e.tabsView[d].path===l.path){e.tabsView[d].params=vt(l.params)?e.tabsView[d].params:l.params,e.tabsView[d].query=vt(l.query)?e.tabsView[d].query:l.query;return}typeof l.meta.title=="string"&&(l.meta.title=l.meta.title.indexOf("pagesTitle.")===-1?l.meta.title:L.global.t(l.meta.title)),e.tabsView.push(l)}}function n(l){e.tabsView.map((d,h)=>{if(d.path==l.path){e.tabsView.splice(h,1);return}})}return{state:e,addTab:t,closeTab:n,closeTabs:(l=!1)=>{l?e.tabsView=[l]:e.tabsView=[]},setActiveRoute:l=>{const d=e.tabsView.findIndex(h=>h.path===l.path);d!==-1&&(e.activeRoute=l,e.activeIndex=d)},setTabsViewRoutes:l=>{e.tabsViewRoutes=or(l)},setAuthNode:(l,d)=>{e.authNode.set(l,d)},fillAuthNode:l=>{e.authNode=l},setFullScreen:l=>{e.tabFullScreen=l}}},{persist:{key:bo,paths:["state.tabFullScreen"]}});function or(e){return e.forEach(t=>{var n;((n=t.meta)==null?void 0:n.menu_type)=="iframe"&&(t.path="/admin/iframe/"+encodeURIComponent(t.path)),t.children&&t.children.length&&(t.children=or(t.children))}),e}const Ve=ke("siteConfig",{state:()=>({siteName:"",recordNumber:"",version:"",cdnUrl:"",apiUrl:"",upload:{mode:"local",maxsize:0,mimetype:"",savename:""},headNav:[],initialize:!1,userInitialize:!1}),actions:{dataFill(e){this.$state=e},setHeadNav(e){this.headNav=e},setInitialize(e){this.initialize=e},setUserInitialize(e){this.userInitialize=e}}});function Wt(e){return ta()?(na(e),!0):!1}function ie(e){return typeof e=="function"?e():Bn(e)}const sr=typeof window<"u",ir=()=>{};function Ro(e,t){function n(...r){return new Promise((a,o)=>{Promise.resolve(e(()=>t.apply(this,r),{fn:t,thisArg:this,args:r})).then(a).catch(o)})}return n}const lr=e=>e();function Fo(e=lr){const t=oe(!0);function n(){t.value=!1}function r(){t.value=!0}const a=(...o)=>{t.value&&e(...o)};return{isActive:Un(t),pause:n,resume:r,eventFilter:a}}function Ht(...e){if(e.length!==1)return ra(...e);const t=e[0];return typeof t=="function"?Un(aa(()=>({get:t,set:ir}))):oe(t)}function Ao(e,t=!0){$n()?$t(e):t?e():X(e)}function Il(e=!1,t={}){const{truthyValue:n=!0,falsyValue:r=!1}=t,a=ea(e),o=oe(e);function s(i){if(arguments.length)return o.value=i,o.value;{const c=ie(n);return o.value=o.value===c?ie(r):c,o.value}}return a?s:[o,s]}var dn=Object.getOwnPropertySymbols,No=Object.prototype.hasOwnProperty,xo=Object.prototype.propertyIsEnumerable,Co=(e,t)=>{var n={};for(var r in e)No.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&dn)for(var r of dn(e))t.indexOf(r)<0&&xo.call(e,r)&&(n[r]=e[r]);return n};function Do(e,t,n={}){const r=n,{eventFilter:a=lr}=r,o=Co(r,["eventFilter"]);return fe(e,Ro(a,t),o)}var Io=Object.defineProperty,Mo=Object.defineProperties,jo=Object.getOwnPropertyDescriptors,je=Object.getOwnPropertySymbols,cr=Object.prototype.hasOwnProperty,ur=Object.prototype.propertyIsEnumerable,mn=(e,t,n)=>t in e?Io(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,Bo=(e,t)=>{for(var n in t||(t={}))cr.call(t,n)&&mn(e,n,t[n]);if(je)for(var n of je(t))ur.call(t,n)&&mn(e,n,t[n]);return e},Uo=(e,t)=>Mo(e,jo(t)),$o=(e,t)=>{var n={};for(var r in e)cr.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&je)for(var r of je(e))t.indexOf(r)<0&&ur.call(e,r)&&(n[r]=e[r]);return n};function Vo(e,t,n={}){const r=n,{eventFilter:a}=r,o=$o(r,["eventFilter"]),{eventFilter:s,pause:i,resume:c,isActive:l}=Fo(a);return{stop:Do(e,t,Uo(Bo({},o),{eventFilter:s})),pause:i,resume:c,isActive:l}}function qt(e){var t;const n=ie(e);return(t=n==null?void 0:n.$el)!=null?t:n}const le=sr?window:void 0,zo=sr?window.document:void 0;function xt(...e){let t,n,r,a;if(typeof e[0]=="string"||Array.isArray(e[0])?([n,r,a]=e,t=le):[t,n,r,a]=e,!t)return ir;Array.isArray(n)||(n=[n]),Array.isArray(r)||(r=[r]);const o=[],s=()=>{o.forEach(d=>d()),o.length=0},i=(d,h,E,v)=>(d.addEventListener(h,E,v),()=>d.removeEventListener(h,E,v)),c=fe(()=>[qt(t),ie(a)],([d,h])=>{s(),d&&o.push(...n.flatMap(E=>r.map(v=>i(d,E,v,h))))},{immediate:!0,flush:"post"}),l=()=>{c(),s()};return Wt(l),l}function Wo(){const e=oe(!1);return $n()&&$t(()=>{e.value=!0}),e}function fr(e){const t=Wo();return Y(()=>(t.value,!!e()))}function Ho(e,t={}){const{window:n=le}=t,r=fr(()=>n&&"matchMedia"in n&&typeof n.matchMedia=="function");let a;const o=oe(!1),s=()=>{a&&("removeEventListener"in a?a.removeEventListener("change",i):a.removeListener(i))},i=()=>{r.value&&(s(),a=n.matchMedia(Ht(e).value),o.value=!!(a!=null&&a.matches),a&&("addEventListener"in a?a.addEventListener("change",i):a.addListener(i)))};return ia(i),Wt(()=>s()),o}const Ae=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{},Ne="__vueuse_ssr_handlers__",qo=Go();function Go(){return Ne in Ae||(Ae[Ne]=Ae[Ne]||{}),Ae[Ne]}function dr(e,t){return qo[e]||t}function Jo(e){return e==null?"any":e instanceof Set?"set":e instanceof Map?"map":e instanceof Date?"date":typeof e=="boolean"?"boolean":typeof e=="string"?"string":typeof e=="object"?"object":Number.isNaN(e)?"any":"number"}var Yo=Object.defineProperty,gn=Object.getOwnPropertySymbols,Ko=Object.prototype.hasOwnProperty,Xo=Object.prototype.propertyIsEnumerable,pn=(e,t,n)=>t in e?Yo(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,hn=(e,t)=>{for(var n in t||(t={}))Ko.call(t,n)&&pn(e,n,t[n]);if(gn)for(var n of gn(t))Xo.call(t,n)&&pn(e,n,t[n]);return e};const Qo={boolean:{read:e=>e==="true",write:e=>String(e)},object:{read:e=>JSON.parse(e),write:e=>JSON.stringify(e)},number:{read:e=>Number.parseFloat(e),write:e=>String(e)},any:{read:e=>e,write:e=>String(e)},string:{read:e=>e,write:e=>String(e)},map:{read:e=>new Map(JSON.parse(e)),write:e=>JSON.stringify(Array.from(e.entries()))},set:{read:e=>new Set(JSON.parse(e)),write:e=>JSON.stringify(Array.from(e))},date:{read:e=>new Date(e),write:e=>e.toISOString()}},bn="vueuse-storage";function Zo(e,t,n,r={}){var a;const{flush:o="pre",deep:s=!0,listenToStorageChanges:i=!0,writeDefaults:c=!0,mergeDefaults:l=!1,shallow:d,window:h=le,eventFilter:E,onError:v=_=>{console.error(_)}}=r,u=(d?sa:oe)(t);if(!n)try{n=dr("getDefaultStorage",()=>{var _;return(_=le)==null?void 0:_.localStorage})()}catch(_){v(_)}if(!n)return u;const m=ie(t),k=Jo(m),O=(a=r.serializer)!=null?a:Qo[k],{pause:P,resume:F}=Vo(u,()=>S(u.value),{flush:o,deep:s,eventFilter:E});return h&&i&&(xt(h,"storage",b),xt(h,bn,j)),b(),u;function S(_){try{if(_==null)n.removeItem(e);else{const A=O.write(_),I=n.getItem(e);I!==A&&(n.setItem(e,A),h&&h.dispatchEvent(new CustomEvent(bn,{detail:{key:e,oldValue:I,newValue:A,storageArea:n}})))}}catch(A){v(A)}}function T(_){const A=_?_.newValue:n.getItem(e);if(A==null)return c&&m!==null&&n.setItem(e,O.write(m)),m;if(!_&&l){const I=O.read(A);return typeof l=="function"?l(I,m):k==="object"&&!Array.isArray(I)?hn(hn({},m),I):I}else return typeof A!="string"?A:O.read(A)}function j(_){b(_.detail)}function b(_){if(!(_&&_.storageArea!==n)){if(_&&_.key==null){u.value=m;return}if(!(_&&_.key!==e)){P();try{u.value=T(_)}catch(A){v(A)}finally{_?X(F):F()}}}}}function es(e){return Ho("(prefers-color-scheme: dark)",e)}var ts=Object.defineProperty,_n=Object.getOwnPropertySymbols,ns=Object.prototype.hasOwnProperty,rs=Object.prototype.propertyIsEnumerable,yn=(e,t,n)=>t in e?ts(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,as=(e,t)=>{for(var n in t||(t={}))ns.call(t,n)&&yn(e,n,t[n]);if(_n)for(var n of _n(t))rs.call(t,n)&&yn(e,n,t[n]);return e};function os(e={}){const{selector:t="html",attribute:n="class",initialValue:r="auto",window:a=le,storage:o,storageKey:s="vueuse-color-scheme",listenToStorageChanges:i=!0,storageRef:c,emitAuto:l,disableTransition:d=!0}=e,h=as({auto:"",light:"light",dark:"dark"},e.modes||{}),E=es({window:a}),v=Y(()=>E.value?"dark":"light"),u=c||(s==null?Ht(r):Zo(s,r,o,{window:a,listenToStorageChanges:i})),m=Y(()=>u.value==="auto"?v.value:u.value),k=dr("updateHTMLAttrs",(S,T,j)=>{const b=typeof S=="string"?a==null?void 0:a.document.querySelector(S):qt(S);if(!b)return;let _;if(d){_=a.document.createElement("style");const A="*,*::before,*::after{-webkit-transition:none!important;-moz-transition:none!important;-o-transition:none!important;-ms-transition:none!important;transition:none!important}";_.appendChild(document.createTextNode(A)),a.document.head.appendChild(_)}if(T==="class"){const A=j.split(/\s/g);Object.values(h).flatMap(I=>(I||"").split(/\s/g)).filter(Boolean).forEach(I=>{A.includes(I)?b.classList.add(I):b.classList.remove(I)})}else b.setAttribute(T,j);d&&(a.getComputedStyle(_).opacity,document.head.removeChild(_))});function O(S){var T;k(t,n,(T=h[S])!=null?T:S)}function P(S){e.onChanged?e.onChanged(S,O):O(S)}fe(m,P,{flush:"post",immediate:!0}),Ao(()=>P(m.value));const F=Y({get(){return l?u.value:m.value},set(S){u.value=S}});try{return Object.assign(F,{store:u,system:v,state:m})}catch{return F}}var vn=Object.getOwnPropertySymbols,ss=Object.prototype.hasOwnProperty,is=Object.prototype.propertyIsEnumerable,ls=(e,t)=>{var n={};for(var r in e)ss.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&vn)for(var r of vn(e))t.indexOf(r)<0&&is.call(e,r)&&(n[r]=e[r]);return n};function cs(e,t,n={}){const r=n,{window:a=le}=r,o=ls(r,["window"]);let s;const i=fr(()=>a&&"MutationObserver"in a),c=()=>{s&&(s.disconnect(),s=void 0)},l=fe(()=>qt(e),h=>{c(),i.value&&a&&h&&(s=new MutationObserver(t),s.observe(h,o))},{immediate:!0}),d=()=>{c(),l()};return Wt(d),{isSupported:i,stop:d}}var us=Object.defineProperty,fs=Object.defineProperties,ds=Object.getOwnPropertyDescriptors,wn=Object.getOwnPropertySymbols,ms=Object.prototype.hasOwnProperty,gs=Object.prototype.propertyIsEnumerable,En=(e,t,n)=>t in e?us(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,ps=(e,t)=>{for(var n in t||(t={}))ms.call(t,n)&&En(e,n,t[n]);if(wn)for(var n of wn(t))gs.call(t,n)&&En(e,n,t[n]);return e},hs=(e,t)=>fs(e,ds(t));function Ml(e={}){const{valueDark:t="dark",valueLight:n=""}=e,r=os(hs(ps({},e),{onChanged:(o,s)=>{var i;e.onChanged?(i=e.onChanged)==null||i.call(e,o==="dark",s,o):s(o)},modes:{dark:t,light:n}}));return Y({get(){return r.value==="dark"},set(o){const s=o?"dark":"light";r.system.value===s?r.value="auto":r.value=s}})}function jl(){const e=oe([]);return e.value.set=t=>{t&&e.value.push(t)},oa(()=>{e.value.length=0}),e}function bs(e=null,t={}){var n,r;const{document:a=zo}=t,o=Ht((n=e??(a==null?void 0:a.title))!=null?n:null),s=e&&typeof e=="function";function i(c){if(!("titleTemplate"in t))return c;const l=t.titleTemplate||"%s";return typeof l=="function"?l(c):ie(l).replace(/%s/g,c)}return fe(o,(c,l)=>{c!==l&&a&&(a.title=i(typeof c=="string"?c:""))},{immediate:!0}),t.observe&&!t.titleTemplate&&a&&!s&&cs((r=a.head)==null?void 0:r.querySelector("title"),()=>{a&&a.title!==o.value&&(o.value=i(a.title))},{childList:!0}),o}function mr(e,t){return function(){return e.apply(t,arguments)}}const{toString:_s}=Object.prototype,{getPrototypeOf:Gt}=Object,ze=(e=>t=>{const n=_s.call(t);return e[n]||(e[n]=n.slice(8,-1).toLowerCase())})(Object.create(null)),Z=e=>(e=e.toLowerCase(),t=>ze(t)===e),We=e=>t=>typeof t===e,{isArray:de}=Array,Ee=We("undefined");function ys(e){return e!==null&&!Ee(e)&&e.constructor!==null&&!Ee(e.constructor)&&G(e.constructor.isBuffer)&&e.constructor.isBuffer(e)}const gr=Z("ArrayBuffer");function vs(e){let t;return typeof ArrayBuffer<"u"&&ArrayBuffer.isView?t=ArrayBuffer.isView(e):t=e&&e.buffer&&gr(e.buffer),t}const ws=We("string"),G=We("function"),pr=We("number"),He=e=>e!==null&&typeof e=="object",Es=e=>e===!0||e===!1,xe=e=>{if(ze(e)!=="object")return!1;const t=Gt(e);return(t===null||t===Object.prototype||Object.getPrototypeOf(t)===null)&&!(Symbol.toStringTag in e)&&!(Symbol.iterator in e)},ks=Z("Date"),Ts=Z("File"),Os=Z("Blob"),Ss=Z("FileList"),Ls=e=>He(e)&&G(e.pipe),Ps=e=>{let t;return e&&(typeof FormData=="function"&&e instanceof FormData||G(e.append)&&((t=ze(e))==="formdata"||t==="object"&&G(e.toString)&&e.toString()==="[object FormData]"))},Rs=Z("URLSearchParams"),Fs=e=>e.trim?e.trim():e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"");function Oe(e,t,{allOwnKeys:n=!1}={}){if(e===null||typeof e>"u")return;let r,a;if(typeof e!="object"&&(e=[e]),de(e))for(r=0,a=e.length;r<a;r++)t.call(null,e[r],r,e);else{const o=n?Object.getOwnPropertyNames(e):Object.keys(e),s=o.length;let i;for(r=0;r<s;r++)i=o[r],t.call(null,e[i],i,e)}}function hr(e,t){t=t.toLowerCase();const n=Object.keys(e);let r=n.length,a;for(;r-- >0;)if(a=n[r],t===a.toLowerCase())return a;return null}const br=(()=>typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:global)(),_r=e=>!Ee(e)&&e!==br;function Ct(){const{caseless:e}=_r(this)&&this||{},t={},n=(r,a)=>{const o=e&&hr(t,a)||a;xe(t[o])&&xe(r)?t[o]=Ct(t[o],r):xe(r)?t[o]=Ct({},r):de(r)?t[o]=r.slice():t[o]=r};for(let r=0,a=arguments.length;r<a;r++)arguments[r]&&Oe(arguments[r],n);return t}const As=(e,t,n,{allOwnKeys:r}={})=>(Oe(t,(a,o)=>{n&&G(a)?e[o]=mr(a,n):e[o]=a},{allOwnKeys:r}),e),Ns=e=>(e.charCodeAt(0)===65279&&(e=e.slice(1)),e),xs=(e,t,n,r)=>{e.prototype=Object.create(t.prototype,r),e.prototype.constructor=e,Object.defineProperty(e,"super",{value:t.prototype}),n&&Object.assign(e.prototype,n)},Cs=(e,t,n,r)=>{let a,o,s;const i={};if(t=t||{},e==null)return t;do{for(a=Object.getOwnPropertyNames(e),o=a.length;o-- >0;)s=a[o],(!r||r(s,e,t))&&!i[s]&&(t[s]=e[s],i[s]=!0);e=n!==!1&&Gt(e)}while(e&&(!n||n(e,t))&&e!==Object.prototype);return t},Ds=(e,t,n)=>{e=String(e),(n===void 0||n>e.length)&&(n=e.length),n-=t.length;const r=e.indexOf(t,n);return r!==-1&&r===n},Is=e=>{if(!e)return null;if(de(e))return e;let t=e.length;if(!pr(t))return null;const n=new Array(t);for(;t-- >0;)n[t]=e[t];return n},Ms=(e=>t=>e&&t instanceof e)(typeof Uint8Array<"u"&&Gt(Uint8Array)),js=(e,t)=>{const r=(e&&e[Symbol.iterator]).call(e);let a;for(;(a=r.next())&&!a.done;){const o=a.value;t.call(e,o[0],o[1])}},Bs=(e,t)=>{let n;const r=[];for(;(n=e.exec(t))!==null;)r.push(n);return r},Us=Z("HTMLFormElement"),$s=e=>e.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(n,r,a){return r.toUpperCase()+a}),kn=(({hasOwnProperty:e})=>(t,n)=>e.call(t,n))(Object.prototype),Vs=Z("RegExp"),yr=(e,t)=>{const n=Object.getOwnPropertyDescriptors(e),r={};Oe(n,(a,o)=>{t(a,o,e)!==!1&&(r[o]=a)}),Object.defineProperties(e,r)},zs=e=>{yr(e,(t,n)=>{if(G(e)&&["arguments","caller","callee"].indexOf(n)!==-1)return!1;const r=e[n];if(G(r)){if(t.enumerable=!1,"writable"in t){t.writable=!1;return}t.set||(t.set=()=>{throw Error("Can not rewrite read-only method '"+n+"'")})}})},Ws=(e,t)=>{const n={},r=a=>{a.forEach(o=>{n[o]=!0})};return de(e)?r(e):r(String(e).split(t)),n},Hs=()=>{},qs=(e,t)=>(e=+e,Number.isFinite(e)?e:t),pt="abcdefghijklmnopqrstuvwxyz",Tn="0123456789",vr={DIGIT:Tn,ALPHA:pt,ALPHA_DIGIT:pt+pt.toUpperCase()+Tn},Gs=(e=16,t=vr.ALPHA_DIGIT)=>{let n="";const{length:r}=t;for(;e--;)n+=t[Math.random()*r|0];return n};function Js(e){return!!(e&&G(e.append)&&e[Symbol.toStringTag]==="FormData"&&e[Symbol.iterator])}const Ys=e=>{const t=new Array(10),n=(r,a)=>{if(He(r)){if(t.indexOf(r)>=0)return;if(!("toJSON"in r)){t[a]=r;const o=de(r)?[]:{};return Oe(r,(s,i)=>{const c=n(s,a+1);!Ee(c)&&(o[i]=c)}),t[a]=void 0,o}}return r};return n(e,0)},Ks=Z("AsyncFunction"),Xs=e=>e&&(He(e)||G(e))&&G(e.then)&&G(e.catch),g={isArray:de,isArrayBuffer:gr,isBuffer:ys,isFormData:Ps,isArrayBufferView:vs,isString:ws,isNumber:pr,isBoolean:Es,isObject:He,isPlainObject:xe,isUndefined:Ee,isDate:ks,isFile:Ts,isBlob:Os,isRegExp:Vs,isFunction:G,isStream:Ls,isURLSearchParams:Rs,isTypedArray:Ms,isFileList:Ss,forEach:Oe,merge:Ct,extend:As,trim:Fs,stripBOM:Ns,inherits:xs,toFlatObject:Cs,kindOf:ze,kindOfTest:Z,endsWith:Ds,toArray:Is,forEachEntry:js,matchAll:Bs,isHTMLForm:Us,hasOwnProperty:kn,hasOwnProp:kn,reduceDescriptors:yr,freezeMethods:zs,toObjectSet:Ws,toCamelCase:$s,noop:Hs,toFiniteNumber:qs,findKey:hr,global:br,isContextDefined:_r,ALPHABET:vr,generateString:Gs,isSpecCompliantForm:Js,toJSONObject:Ys,isAsyncFn:Ks,isThenable:Xs};function D(e,t,n,r,a){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=new Error().stack,this.message=e,this.name="AxiosError",t&&(this.code=t),n&&(this.config=n),r&&(this.request=r),a&&(this.response=a)}g.inherits(D,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:g.toJSONObject(this.config),code:this.code,status:this.response&&this.response.status?this.response.status:null}}});const wr=D.prototype,Er={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(e=>{Er[e]={value:e}});Object.defineProperties(D,Er);Object.defineProperty(wr,"isAxiosError",{value:!0});D.from=(e,t,n,r,a,o)=>{const s=Object.create(wr);return g.toFlatObject(e,s,function(c){return c!==Error.prototype},i=>i!=="isAxiosError"),D.call(s,e.message,t,n,r,a),s.cause=e,s.name=e.name,o&&Object.assign(s,o),s};const Qs=null;function Dt(e){return g.isPlainObject(e)||g.isArray(e)}function kr(e){return g.endsWith(e,"[]")?e.slice(0,-2):e}function On(e,t,n){return e?e.concat(t).map(function(a,o){return a=kr(a),!n&&o?"["+a+"]":a}).join(n?".":""):t}function Zs(e){return g.isArray(e)&&!e.some(Dt)}const ei=g.toFlatObject(g,{},null,function(t){return/^is[A-Z]/.test(t)});function qe(e,t,n){if(!g.isObject(e))throw new TypeError("target must be an object");t=t||new FormData,n=g.toFlatObject(n,{metaTokens:!0,dots:!1,indexes:!1},!1,function(m,k){return!g.isUndefined(k[m])});const r=n.metaTokens,a=n.visitor||d,o=n.dots,s=n.indexes,c=(n.Blob||typeof Blob<"u"&&Blob)&&g.isSpecCompliantForm(t);if(!g.isFunction(a))throw new TypeError("visitor must be a function");function l(u){if(u===null)return"";if(g.isDate(u))return u.toISOString();if(!c&&g.isBlob(u))throw new D("Blob is not supported. Use a Buffer instead.");return g.isArrayBuffer(u)||g.isTypedArray(u)?c&&typeof Blob=="function"?new Blob([u]):Buffer.from(u):u}function d(u,m,k){let O=u;if(u&&!k&&typeof u=="object"){if(g.endsWith(m,"{}"))m=r?m:m.slice(0,-2),u=JSON.stringify(u);else if(g.isArray(u)&&Zs(u)||(g.isFileList(u)||g.endsWith(m,"[]"))&&(O=g.toArray(u)))return m=kr(m),O.forEach(function(F,S){!(g.isUndefined(F)||F===null)&&t.append(s===!0?On([m],S,o):s===null?m:m+"[]",l(F))}),!1}return Dt(u)?!0:(t.append(On(k,m,o),l(u)),!1)}const h=[],E=Object.assign(ei,{defaultVisitor:d,convertValue:l,isVisitable:Dt});function v(u,m){if(!g.isUndefined(u)){if(h.indexOf(u)!==-1)throw Error("Circular reference detected in "+m.join("."));h.push(u),g.forEach(u,function(O,P){(!(g.isUndefined(O)||O===null)&&a.call(t,O,g.isString(P)?P.trim():P,m,E))===!0&&v(O,m?m.concat(P):[P])}),h.pop()}}if(!g.isObject(e))throw new TypeError("data must be an object");return v(e),t}function Sn(e){const t={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(e).replace(/[!'()~]|%20|%00/g,function(r){return t[r]})}function Jt(e,t){this._pairs=[],e&&qe(e,this,t)}const Tr=Jt.prototype;Tr.append=function(t,n){this._pairs.push([t,n])};Tr.toString=function(t){const n=t?function(r){return t.call(this,r,Sn)}:Sn;return this._pairs.map(function(a){return n(a[0])+"="+n(a[1])},"").join("&")};function ti(e){return encodeURIComponent(e).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function Or(e,t,n){if(!t)return e;const r=n&&n.encode||ti,a=n&&n.serialize;let o;if(a?o=a(t,n):o=g.isURLSearchParams(t)?t.toString():new Jt(t,n).toString(r),o){const s=e.indexOf("#");s!==-1&&(e=e.slice(0,s)),e+=(e.indexOf("?")===-1?"?":"&")+o}return e}class ni{constructor(){this.handlers=[]}use(t,n,r){return this.handlers.push({fulfilled:t,rejected:n,synchronous:r?r.synchronous:!1,runWhen:r?r.runWhen:null}),this.handlers.length-1}eject(t){this.handlers[t]&&(this.handlers[t]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(t){g.forEach(this.handlers,function(r){r!==null&&t(r)})}}const Ln=ni,Sr={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},ri=typeof URLSearchParams<"u"?URLSearchParams:Jt,ai=typeof FormData<"u"?FormData:null,oi=typeof Blob<"u"?Blob:null,si=(()=>{let e;return typeof navigator<"u"&&((e=navigator.product)==="ReactNative"||e==="NativeScript"||e==="NS")?!1:typeof window<"u"&&typeof document<"u"})(),ii=(()=>typeof WorkerGlobalScope<"u"&&self instanceof WorkerGlobalScope&&typeof self.importScripts=="function")(),K={isBrowser:!0,classes:{URLSearchParams:ri,FormData:ai,Blob:oi},isStandardBrowserEnv:si,isStandardBrowserWebWorkerEnv:ii,protocols:["http","https","file","blob","url","data"]};function li(e,t){return qe(e,new K.classes.URLSearchParams,Object.assign({visitor:function(n,r,a,o){return K.isNode&&g.isBuffer(n)?(this.append(r,n.toString("base64")),!1):o.defaultVisitor.apply(this,arguments)}},t))}function ci(e){return g.matchAll(/\w+|\[(\w*)]/g,e).map(t=>t[0]==="[]"?"":t[1]||t[0])}function ui(e){const t={},n=Object.keys(e);let r;const a=n.length;let o;for(r=0;r<a;r++)o=n[r],t[o]=e[o];return t}function Lr(e){function t(n,r,a,o){let s=n[o++];const i=Number.isFinite(+s),c=o>=n.length;return s=!s&&g.isArray(a)?a.length:s,c?(g.hasOwnProp(a,s)?a[s]=[a[s],r]:a[s]=r,!i):((!a[s]||!g.isObject(a[s]))&&(a[s]=[]),t(n,r,a[s],o)&&g.isArray(a[s])&&(a[s]=ui(a[s])),!i)}if(g.isFormData(e)&&g.isFunction(e.entries)){const n={};return g.forEachEntry(e,(r,a)=>{t(ci(r),a,n,0)}),n}return null}const fi={"Content-Type":void 0};function di(e,t,n){if(g.isString(e))try{return(t||JSON.parse)(e),g.trim(e)}catch(r){if(r.name!=="SyntaxError")throw r}return(n||JSON.stringify)(e)}const Ge={transitional:Sr,adapter:["xhr","http"],transformRequest:[function(t,n){const r=n.getContentType()||"",a=r.indexOf("application/json")>-1,o=g.isObject(t);if(o&&g.isHTMLForm(t)&&(t=new FormData(t)),g.isFormData(t))return a&&a?JSON.stringify(Lr(t)):t;if(g.isArrayBuffer(t)||g.isBuffer(t)||g.isStream(t)||g.isFile(t)||g.isBlob(t))return t;if(g.isArrayBufferView(t))return t.buffer;if(g.isURLSearchParams(t))return n.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),t.toString();let i;if(o){if(r.indexOf("application/x-www-form-urlencoded")>-1)return li(t,this.formSerializer).toString();if((i=g.isFileList(t))||r.indexOf("multipart/form-data")>-1){const c=this.env&&this.env.FormData;return qe(i?{"files[]":t}:t,c&&new c,this.formSerializer)}}return o||a?(n.setContentType("application/json",!1),di(t)):t}],transformResponse:[function(t){const n=this.transitional||Ge.transitional,r=n&&n.forcedJSONParsing,a=this.responseType==="json";if(t&&g.isString(t)&&(r&&!this.responseType||a)){const s=!(n&&n.silentJSONParsing)&&a;try{return JSON.parse(t)}catch(i){if(s)throw i.name==="SyntaxError"?D.from(i,D.ERR_BAD_RESPONSE,this,null,this.response):i}}return t}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:K.classes.FormData,Blob:K.classes.Blob},validateStatus:function(t){return t>=200&&t<300},headers:{common:{Accept:"application/json, text/plain, */*"}}};g.forEach(["delete","get","head"],function(t){Ge.headers[t]={}});g.forEach(["post","put","patch"],function(t){Ge.headers[t]=g.merge(fi)});const Yt=Ge,mi=g.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),gi=e=>{const t={};let n,r,a;return e&&e.split(`
`).forEach(function(s){a=s.indexOf(":"),n=s.substring(0,a).trim().toLowerCase(),r=s.substring(a+1).trim(),!(!n||t[n]&&mi[n])&&(n==="set-cookie"?t[n]?t[n].push(r):t[n]=[r]:t[n]=t[n]?t[n]+", "+r:r)}),t},Pn=Symbol("internals");function he(e){return e&&String(e).trim().toLowerCase()}function Ce(e){return e===!1||e==null?e:g.isArray(e)?e.map(Ce):String(e)}function pi(e){const t=Object.create(null),n=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let r;for(;r=n.exec(e);)t[r[1]]=r[2];return t}const hi=e=>/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(e.trim());function ht(e,t,n,r,a){if(g.isFunction(r))return r.call(this,t,n);if(a&&(t=n),!!g.isString(t)){if(g.isString(r))return t.indexOf(r)!==-1;if(g.isRegExp(r))return r.test(t)}}function bi(e){return e.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(t,n,r)=>n.toUpperCase()+r)}function _i(e,t){const n=g.toCamelCase(" "+t);["get","set","has"].forEach(r=>{Object.defineProperty(e,r+n,{value:function(a,o,s){return this[r].call(this,t,a,o,s)},configurable:!0})})}class Je{constructor(t){t&&this.set(t)}set(t,n,r){const a=this;function o(i,c,l){const d=he(c);if(!d)throw new Error("header name must be a non-empty string");const h=g.findKey(a,d);(!h||a[h]===void 0||l===!0||l===void 0&&a[h]!==!1)&&(a[h||c]=Ce(i))}const s=(i,c)=>g.forEach(i,(l,d)=>o(l,d,c));return g.isPlainObject(t)||t instanceof this.constructor?s(t,n):g.isString(t)&&(t=t.trim())&&!hi(t)?s(gi(t),n):t!=null&&o(n,t,r),this}get(t,n){if(t=he(t),t){const r=g.findKey(this,t);if(r){const a=this[r];if(!n)return a;if(n===!0)return pi(a);if(g.isFunction(n))return n.call(this,a,r);if(g.isRegExp(n))return n.exec(a);throw new TypeError("parser must be boolean|regexp|function")}}}has(t,n){if(t=he(t),t){const r=g.findKey(this,t);return!!(r&&this[r]!==void 0&&(!n||ht(this,this[r],r,n)))}return!1}delete(t,n){const r=this;let a=!1;function o(s){if(s=he(s),s){const i=g.findKey(r,s);i&&(!n||ht(r,r[i],i,n))&&(delete r[i],a=!0)}}return g.isArray(t)?t.forEach(o):o(t),a}clear(t){const n=Object.keys(this);let r=n.length,a=!1;for(;r--;){const o=n[r];(!t||ht(this,this[o],o,t,!0))&&(delete this[o],a=!0)}return a}normalize(t){const n=this,r={};return g.forEach(this,(a,o)=>{const s=g.findKey(r,o);if(s){n[s]=Ce(a),delete n[o];return}const i=t?bi(o):String(o).trim();i!==o&&delete n[o],n[i]=Ce(a),r[i]=!0}),this}concat(...t){return this.constructor.concat(this,...t)}toJSON(t){const n=Object.create(null);return g.forEach(this,(r,a)=>{r!=null&&r!==!1&&(n[a]=t&&g.isArray(r)?r.join(", "):r)}),n}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(([t,n])=>t+": "+n).join(`
`)}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(t){return t instanceof this?t:new this(t)}static concat(t,...n){const r=new this(t);return n.forEach(a=>r.set(a)),r}static accessor(t){const r=(this[Pn]=this[Pn]={accessors:{}}).accessors,a=this.prototype;function o(s){const i=he(s);r[i]||(_i(a,s),r[i]=!0)}return g.isArray(t)?t.forEach(o):o(t),this}}Je.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]);g.freezeMethods(Je.prototype);g.freezeMethods(Je);const ee=Je;function bt(e,t){const n=this||Yt,r=t||n,a=ee.from(r.headers);let o=r.data;return g.forEach(e,function(i){o=i.call(n,o,a.normalize(),t?t.status:void 0)}),a.normalize(),o}function Pr(e){return!!(e&&e.__CANCEL__)}function Se(e,t,n){D.call(this,e??"canceled",D.ERR_CANCELED,t,n),this.name="CanceledError"}g.inherits(Se,D,{__CANCEL__:!0});function yi(e,t,n){const r=n.config.validateStatus;!n.status||!r||r(n.status)?e(n):t(new D("Request failed with status code "+n.status,[D.ERR_BAD_REQUEST,D.ERR_BAD_RESPONSE][Math.floor(n.status/100)-4],n.config,n.request,n))}const vi=K.isStandardBrowserEnv?function(){return{write:function(n,r,a,o,s,i){const c=[];c.push(n+"="+encodeURIComponent(r)),g.isNumber(a)&&c.push("expires="+new Date(a).toGMTString()),g.isString(o)&&c.push("path="+o),g.isString(s)&&c.push("domain="+s),i===!0&&c.push("secure"),document.cookie=c.join("; ")},read:function(n){const r=document.cookie.match(new RegExp("(^|;\\s*)("+n+")=([^;]*)"));return r?decodeURIComponent(r[3]):null},remove:function(n){this.write(n,"",Date.now()-864e5)}}}():function(){return{write:function(){},read:function(){return null},remove:function(){}}}();function wi(e){return/^([a-z][a-z\d+\-.]*:)?\/\//i.test(e)}function Ei(e,t){return t?e.replace(/\/+$/,"")+"/"+t.replace(/^\/+/,""):e}function Rr(e,t){return e&&!wi(t)?Ei(e,t):t}const ki=K.isStandardBrowserEnv?function(){const t=/(msie|trident)/i.test(navigator.userAgent),n=document.createElement("a");let r;function a(o){let s=o;return t&&(n.setAttribute("href",s),s=n.href),n.setAttribute("href",s),{href:n.href,protocol:n.protocol?n.protocol.replace(/:$/,""):"",host:n.host,search:n.search?n.search.replace(/^\?/,""):"",hash:n.hash?n.hash.replace(/^#/,""):"",hostname:n.hostname,port:n.port,pathname:n.pathname.charAt(0)==="/"?n.pathname:"/"+n.pathname}}return r=a(window.location.href),function(s){const i=g.isString(s)?a(s):s;return i.protocol===r.protocol&&i.host===r.host}}():function(){return function(){return!0}}();function Ti(e){const t=/^([-+\w]{1,25})(:?\/\/|:)/.exec(e);return t&&t[1]||""}function Oi(e,t){e=e||10;const n=new Array(e),r=new Array(e);let a=0,o=0,s;return t=t!==void 0?t:1e3,function(c){const l=Date.now(),d=r[o];s||(s=l),n[a]=c,r[a]=l;let h=o,E=0;for(;h!==a;)E+=n[h++],h=h%e;if(a=(a+1)%e,a===o&&(o=(o+1)%e),l-s<t)return;const v=d&&l-d;return v?Math.round(E*1e3/v):void 0}}function Rn(e,t){let n=0;const r=Oi(50,250);return a=>{const o=a.loaded,s=a.lengthComputable?a.total:void 0,i=o-n,c=r(i),l=o<=s;n=o;const d={loaded:o,total:s,progress:s?o/s:void 0,bytes:i,rate:c||void 0,estimated:c&&s&&l?(s-o)/c:void 0,event:a};d[t?"download":"upload"]=!0,e(d)}}const Si=typeof XMLHttpRequest<"u",Li=Si&&function(e){return new Promise(function(n,r){let a=e.data;const o=ee.from(e.headers).normalize(),s=e.responseType;let i;function c(){e.cancelToken&&e.cancelToken.unsubscribe(i),e.signal&&e.signal.removeEventListener("abort",i)}g.isFormData(a)&&(K.isStandardBrowserEnv||K.isStandardBrowserWebWorkerEnv?o.setContentType(!1):o.setContentType("multipart/form-data;",!1));let l=new XMLHttpRequest;if(e.auth){const v=e.auth.username||"",u=e.auth.password?unescape(encodeURIComponent(e.auth.password)):"";o.set("Authorization","Basic "+btoa(v+":"+u))}const d=Rr(e.baseURL,e.url);l.open(e.method.toUpperCase(),Or(d,e.params,e.paramsSerializer),!0),l.timeout=e.timeout;function h(){if(!l)return;const v=ee.from("getAllResponseHeaders"in l&&l.getAllResponseHeaders()),m={data:!s||s==="text"||s==="json"?l.responseText:l.response,status:l.status,statusText:l.statusText,headers:v,config:e,request:l};yi(function(O){n(O),c()},function(O){r(O),c()},m),l=null}if("onloadend"in l?l.onloadend=h:l.onreadystatechange=function(){!l||l.readyState!==4||l.status===0&&!(l.responseURL&&l.responseURL.indexOf("file:")===0)||setTimeout(h)},l.onabort=function(){l&&(r(new D("Request aborted",D.ECONNABORTED,e,l)),l=null)},l.onerror=function(){r(new D("Network Error",D.ERR_NETWORK,e,l)),l=null},l.ontimeout=function(){let u=e.timeout?"timeout of "+e.timeout+"ms exceeded":"timeout exceeded";const m=e.transitional||Sr;e.timeoutErrorMessage&&(u=e.timeoutErrorMessage),r(new D(u,m.clarifyTimeoutError?D.ETIMEDOUT:D.ECONNABORTED,e,l)),l=null},K.isStandardBrowserEnv){const v=(e.withCredentials||ki(d))&&e.xsrfCookieName&&vi.read(e.xsrfCookieName);v&&o.set(e.xsrfHeaderName,v)}a===void 0&&o.setContentType(null),"setRequestHeader"in l&&g.forEach(o.toJSON(),function(u,m){l.setRequestHeader(m,u)}),g.isUndefined(e.withCredentials)||(l.withCredentials=!!e.withCredentials),s&&s!=="json"&&(l.responseType=e.responseType),typeof e.onDownloadProgress=="function"&&l.addEventListener("progress",Rn(e.onDownloadProgress,!0)),typeof e.onUploadProgress=="function"&&l.upload&&l.upload.addEventListener("progress",Rn(e.onUploadProgress)),(e.cancelToken||e.signal)&&(i=v=>{l&&(r(!v||v.type?new Se(null,e,l):v),l.abort(),l=null)},e.cancelToken&&e.cancelToken.subscribe(i),e.signal&&(e.signal.aborted?i():e.signal.addEventListener("abort",i)));const E=Ti(d);if(E&&K.protocols.indexOf(E)===-1){r(new D("Unsupported protocol "+E+":",D.ERR_BAD_REQUEST,e));return}l.send(a||null)})},De={http:Qs,xhr:Li};g.forEach(De,(e,t)=>{if(e){try{Object.defineProperty(e,"name",{value:t})}catch{}Object.defineProperty(e,"adapterName",{value:t})}});const Pi={getAdapter:e=>{e=g.isArray(e)?e:[e];const{length:t}=e;let n,r;for(let a=0;a<t&&(n=e[a],!(r=g.isString(n)?De[n.toLowerCase()]:n));a++);if(!r)throw r===!1?new D(`Adapter ${n} is not supported by the environment`,"ERR_NOT_SUPPORT"):new Error(g.hasOwnProp(De,n)?`Adapter '${n}' is not available in the build`:`Unknown adapter '${n}'`);if(!g.isFunction(r))throw new TypeError("adapter is not a function");return r},adapters:De};function _t(e){if(e.cancelToken&&e.cancelToken.throwIfRequested(),e.signal&&e.signal.aborted)throw new Se(null,e)}function Fn(e){return _t(e),e.headers=ee.from(e.headers),e.data=bt.call(e,e.transformRequest),["post","put","patch"].indexOf(e.method)!==-1&&e.headers.setContentType("application/x-www-form-urlencoded",!1),Pi.getAdapter(e.adapter||Yt.adapter)(e).then(function(r){return _t(e),r.data=bt.call(e,e.transformResponse,r),r.headers=ee.from(r.headers),r},function(r){return Pr(r)||(_t(e),r&&r.response&&(r.response.data=bt.call(e,e.transformResponse,r.response),r.response.headers=ee.from(r.response.headers))),Promise.reject(r)})}const An=e=>e instanceof ee?e.toJSON():e;function ce(e,t){t=t||{};const n={};function r(l,d,h){return g.isPlainObject(l)&&g.isPlainObject(d)?g.merge.call({caseless:h},l,d):g.isPlainObject(d)?g.merge({},d):g.isArray(d)?d.slice():d}function a(l,d,h){if(g.isUndefined(d)){if(!g.isUndefined(l))return r(void 0,l,h)}else return r(l,d,h)}function o(l,d){if(!g.isUndefined(d))return r(void 0,d)}function s(l,d){if(g.isUndefined(d)){if(!g.isUndefined(l))return r(void 0,l)}else return r(void 0,d)}function i(l,d,h){if(h in t)return r(l,d);if(h in e)return r(void 0,l)}const c={url:o,method:o,data:o,baseURL:s,transformRequest:s,transformResponse:s,paramsSerializer:s,timeout:s,timeoutMessage:s,withCredentials:s,adapter:s,responseType:s,xsrfCookieName:s,xsrfHeaderName:s,onUploadProgress:s,onDownloadProgress:s,decompress:s,maxContentLength:s,maxBodyLength:s,beforeRedirect:s,transport:s,httpAgent:s,httpsAgent:s,cancelToken:s,socketPath:s,responseEncoding:s,validateStatus:i,headers:(l,d)=>a(An(l),An(d),!0)};return g.forEach(Object.keys(Object.assign({},e,t)),function(d){const h=c[d]||a,E=h(e[d],t[d],d);g.isUndefined(E)&&h!==i||(n[d]=E)}),n}const Fr="1.4.0",Kt={};["object","boolean","number","function","string","symbol"].forEach((e,t)=>{Kt[e]=function(r){return typeof r===e||"a"+(t<1?"n ":" ")+e}});const Nn={};Kt.transitional=function(t,n,r){function a(o,s){return"[Axios v"+Fr+"] Transitional option '"+o+"'"+s+(r?". "+r:"")}return(o,s,i)=>{if(t===!1)throw new D(a(s," has been removed"+(n?" in "+n:"")),D.ERR_DEPRECATED);return n&&!Nn[s]&&(Nn[s]=!0,console.warn(a(s," has been deprecated since v"+n+" and will be removed in the near future"))),t?t(o,s,i):!0}};function Ri(e,t,n){if(typeof e!="object")throw new D("options must be an object",D.ERR_BAD_OPTION_VALUE);const r=Object.keys(e);let a=r.length;for(;a-- >0;){const o=r[a],s=t[o];if(s){const i=e[o],c=i===void 0||s(i,o,e);if(c!==!0)throw new D("option "+o+" must be "+c,D.ERR_BAD_OPTION_VALUE);continue}if(n!==!0)throw new D("Unknown option "+o,D.ERR_BAD_OPTION)}}const It={assertOptions:Ri,validators:Kt},re=It.validators;class Be{constructor(t){this.defaults=t,this.interceptors={request:new Ln,response:new Ln}}request(t,n){typeof t=="string"?(n=n||{},n.url=t):n=t||{},n=ce(this.defaults,n);const{transitional:r,paramsSerializer:a,headers:o}=n;r!==void 0&&It.assertOptions(r,{silentJSONParsing:re.transitional(re.boolean),forcedJSONParsing:re.transitional(re.boolean),clarifyTimeoutError:re.transitional(re.boolean)},!1),a!=null&&(g.isFunction(a)?n.paramsSerializer={serialize:a}:It.assertOptions(a,{encode:re.function,serialize:re.function},!0)),n.method=(n.method||this.defaults.method||"get").toLowerCase();let s;s=o&&g.merge(o.common,o[n.method]),s&&g.forEach(["delete","get","head","post","put","patch","common"],u=>{delete o[u]}),n.headers=ee.concat(s,o);const i=[];let c=!0;this.interceptors.request.forEach(function(m){typeof m.runWhen=="function"&&m.runWhen(n)===!1||(c=c&&m.synchronous,i.unshift(m.fulfilled,m.rejected))});const l=[];this.interceptors.response.forEach(function(m){l.push(m.fulfilled,m.rejected)});let d,h=0,E;if(!c){const u=[Fn.bind(this),void 0];for(u.unshift.apply(u,i),u.push.apply(u,l),E=u.length,d=Promise.resolve(n);h<E;)d=d.then(u[h++],u[h++]);return d}E=i.length;let v=n;for(h=0;h<E;){const u=i[h++],m=i[h++];try{v=u(v)}catch(k){m.call(this,k);break}}try{d=Fn.call(this,v)}catch(u){return Promise.reject(u)}for(h=0,E=l.length;h<E;)d=d.then(l[h++],l[h++]);return d}getUri(t){t=ce(this.defaults,t);const n=Rr(t.baseURL,t.url);return Or(n,t.params,t.paramsSerializer)}}g.forEach(["delete","get","head","options"],function(t){Be.prototype[t]=function(n,r){return this.request(ce(r||{},{method:t,url:n,data:(r||{}).data}))}});g.forEach(["post","put","patch"],function(t){function n(r){return function(o,s,i){return this.request(ce(i||{},{method:t,headers:r?{"Content-Type":"multipart/form-data"}:{},url:o,data:s}))}}Be.prototype[t]=n(),Be.prototype[t+"Form"]=n(!0)});const Ie=Be;class Xt{constructor(t){if(typeof t!="function")throw new TypeError("executor must be a function.");let n;this.promise=new Promise(function(o){n=o});const r=this;this.promise.then(a=>{if(!r._listeners)return;let o=r._listeners.length;for(;o-- >0;)r._listeners[o](a);r._listeners=null}),this.promise.then=a=>{let o;const s=new Promise(i=>{r.subscribe(i),o=i}).then(a);return s.cancel=function(){r.unsubscribe(o)},s},t(function(o,s,i){r.reason||(r.reason=new Se(o,s,i),n(r.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(t){if(this.reason){t(this.reason);return}this._listeners?this._listeners.push(t):this._listeners=[t]}unsubscribe(t){if(!this._listeners)return;const n=this._listeners.indexOf(t);n!==-1&&this._listeners.splice(n,1)}static source(){let t;return{token:new Xt(function(a){t=a}),cancel:t}}}const Fi=Xt;function Ai(e){return function(n){return e.apply(null,n)}}function Ni(e){return g.isObject(e)&&e.isAxiosError===!0}const Mt={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(Mt).forEach(([e,t])=>{Mt[t]=e});const xi=Mt;function Ar(e){const t=new Ie(e),n=mr(Ie.prototype.request,t);return g.extend(n,Ie.prototype,t,{allOwnKeys:!0}),g.extend(n,t,null,{allOwnKeys:!0}),n.create=function(a){return Ar(ce(e,a))},n}const $=Ar(Yt);$.Axios=Ie;$.CanceledError=Se;$.CancelToken=Fi;$.isCancel=Pr;$.VERSION=Fr;$.toFormData=qe;$.AxiosError=D;$.Cancel=$.CanceledError;$.all=function(t){return Promise.all(t)};$.spread=Ai;$.isAxiosError=Ni;$.mergeConfig=ce;$.AxiosHeaders=ee;$.formToJSON=e=>Lr(g.isHTMLForm(e)?new FormData(e):e);$.HttpStatusCode=xi;$.default=$;const Qt=$,Ye=ke("adminInfo",{state:()=>({id:0,username:"",nickname:"",avatar:"",last_login_time:"",token:"",refresh_token:"",super:!1}),actions:{dataFill(e){this.$state={...this.$state,...e}},removeToken(){this.token="",this.refresh_token=""},setToken(e,t){const n=t=="auth"?"token":"refresh_token";this[n]=e},getToken(e="auth"){return e==="auth"?this.token:this.refresh_token},setSuper(e){this.super=e}},persist:{key:po}}),Ci=()=>"disable";function Di(e,t={},n={}){return new Promise((r,a)=>{console.log(e,t,n),a("未定义")})}const jt=[];for(let e=0;e<=15;e++)jt[e]=e.toString(16);function Nr(){let e="";for(let t=1;t<=36;t++)t===9||t===14||t===19||t===24?e+="-":t===15?e+=4:t===20?e+=jt[Math.random()*4|8]:e+=jt[Math.random()*16|0];return e}function Bl(e=""){const t=Date.now(),n=Math.floor(Math.random()*1e9);return window.unique||(window.unique=0),window.unique++,e+"_"+n+window.unique+String(t)}const Ii="/admin/ajax/upload",Mi="/admin/ajax/buildSuffixSvg",ji="/admin/ajax/area",Bi="/admin/ajax/getTableFieldList",Ui="/admin/Terminal/index",$i="/admin/ajax/changeTerminalConfig",Vi="/admin/ajax/clearCache",zi="/api/common/clickCaptcha",Wi="/api/common/checkClickCaptcha",Hi="/api/common/refreshToken",qi="/api/ajax/upload",Gi="/api/ajax/buildSuffixSvg",Ji="/api/ajax/area";function Ul(e,t={},n=!1,r={}){let a="";const o=e.get("file"),s=Ve();return!o.name||typeof o.size>"u"?a=L.global.t("utils.The data of the uploaded file is incomplete!"):sl(o.name,o.type)?o.size>s.upload.maxsize&&(a=L.global.t("utils.The size of the uploaded file exceeds the allowed range!")):a=L.global.t("utils.The type of uploaded file is not allowed!"),a?new Promise((i,c)=>{we({type:"error",message:a}),c(a)}):!n&&Ci()=="enable"?Di(e,t,r):H({url:ue()?Ii:qi,method:"POST",data:e,params:t,timeout:0,...r})}function $l(e,t=""){const n=Ye();return me()+(ue()?Mi:Gi)+"?batoken="+n.getToken()+"&suffix="+e+(t?"&background="+t:"")+"&server=1"}function Vl(e){const t={};return e[0]&&(t.province=e[0]),e[1]&&(t.city=e[1]),t.uuid=Nr(),H({url:ue()?ji:Ji,method:"GET",params:t})}function zl(e){return H({url:Vi,method:"POST",data:{type:e}},{showSuccessMessage:!0})}function Yi(e,t,n){const r=Ye();return me()+Ui+"?command="+e+"&uuid="+t+"&extend="+n+"&batoken="+r.getToken()+"&server=1"}function Wl(e){return H({url:$i,method:"POST",data:e},{loading:!0})}function Hl(e,t,n){return H({url:e,method:"get",params:Object.assign(n,{select:!0,quickSearch:t})})}function ql(e){return H({url:zi,method:"get",params:{id:e}})}function Gl(e,t,n){return H({url:Wi,method:"post",data:{id:e,info:t,unset:n}},{showCodeMessage:!1})}function Jl(e,t=!0){return H({url:Bi,method:"get",params:{table:e,clean:t?1:0}})}function Ki(){const e=Ye();return H({url:Hi,method:"POST",data:{refreshToken:e.getToken("refresh")}})}class Yl{constructor(t){pe(this,"controllerUrl");pe(this,"actionUrl");this.controllerUrl=t,this.actionUrl=new Map([["index",t+"index"],["add",t+"add"],["edit",t+"edit"],["del",t+"del"],["sortable",t+"sortable"]])}index(t={}){return H({url:this.actionUrl.get("index"),method:"get",params:t})}edit(t){return H({url:this.actionUrl.get("edit"),method:"get",params:t})}del(t){return H({url:this.actionUrl.get("del"),method:"DELETE",params:{ids:t}},{showSuccessMessage:!0})}postData(t,n){return H({url:this.actionUrl.has(t)?this.actionUrl.get(t):this.controllerUrl+t,method:"post",data:n},{showSuccessMessage:!0})}sortableApi(t,n){return H({url:this.actionUrl.get("sortable"),method:"post",data:{id:t,targetId:n}})}}window.requests=[];window.tokenRefreshing=!1;const ve=new Map,ae={target:null,count:0},me=()=>window.location.protocol+"//"+window.location.host,Kl=()=>{const e=me();return new URL(e).port};function H(e,t={},n={}){const r=Te(),a=Ye(),o=Qt.create({baseURL:me(),timeout:1e3*10,headers:{"think-lang":r.lang.defaultLang,server:!0},responseType:"json"});return t=Object.assign({CancelDuplicateRequest:!0,loading:!1,reductDataFormat:!0,showErrorMessage:!0,showCodeMessage:!0,showSuccessMessage:!1,anotherToken:""},t),o.interceptors.request.use(s=>{if(yt(s),t.CancelDuplicateRequest&&Qi(s),t.loading&&(ae.count++,ae.count===1&&(ae.target=la.service(n))),s.headers){const i=a.getToken();i&&(s.headers.batoken=i);const c=t.anotherToken;c&&(s.headers["ba-user-token"]=c)}return s},s=>Promise.reject(s)),o.interceptors.response.use(s=>{if(yt(s.config),t.loading&&xn(t),s.config.responseType=="json"){if(s.data&&s.data.code!==1)return s.data.code==409?window.tokenRefreshing?new Promise(i=>{window.requests.push((c,l)=>{l=="admin-refresh"?s.headers.batoken=`${c}`:s.headers["ba-user-token"]=`${c}`,i(o(s.config))})}):(window.tokenRefreshing=!0,Ki().then(i=>(i.data.type=="admin-refresh"&&(a.setToken(i.data.token,"auth"),s.headers.batoken=`${i.data.token}`,window.requests.forEach(c=>c(i.data.token,"admin-refresh"))),window.requests=[],o(s.config))).catch(i=>{if(ue())return a.removeToken(),q.currentRoute.value.name!="adminLogin"?(q.push({name:"adminLogin"}),Promise.reject(i)):(s.headers.batoken="",window.requests.forEach(c=>c("","admin-refresh")),window.requests=[],o(s.config))}).finally(()=>{window.tokenRefreshing=!1})):(t.showCodeMessage&&we({type:"error",message:s.data.msg}),s.data.code==302&&(ue()&&a.removeToken(),s.data.data.routeName?q.push({name:s.data.data.routeName}):s.data.data.routePath&&q.push({path:s.data.data.routePath})),Promise.reject(s.data));t.showSuccessMessage&&s.data&&s.data.code==1&&we({message:s.data.msg?s.data.msg:L.global.t("axios.Operation successful"),type:"success"})}return t.reductDataFormat?s.data:s},s=>(s.config&&yt(s.config),t.loading&&xn(t),t.showErrorMessage&&Xi(s),Promise.reject(s))),o(e)}function Xi(e){if(Qt.isCancel(e))return console.error(L.global.t("axios.Automatic cancellation due to duplicate request:")+e.message);let t="";if(e&&e.response)switch(e.response.status){case 302:t=L.global.t("axios.Interface redirected!");break;case 400:t=L.global.t("axios.Incorrect parameter!");break;case 401:t=L.global.t("axios.You do not have permission to operate!");break;case 403:t=L.global.t("axios.You do not have permission to operate!");break;case 404:t=L.global.t("axios.Error requesting address:")+e.response.config.url;break;case 408:t=L.global.t("axios.Request timed out!");break;case 409:t=L.global.t("axios.The same data already exists in the system!");break;case 500:t=L.global.t("axios.Server internal error!");break;case 501:t=L.global.t("axios.Service not implemented!");break;case 502:t=L.global.t("axios.Gateway error!");break;case 503:t=L.global.t("axios.Service unavailable!");break;case 504:t=L.global.t("axios.The service is temporarily unavailable Please try again later!");break;case 505:t=L.global.t("axios.HTTP version is not supported!");break;default:t=L.global.t("axios.Abnormal problem, please contact the website administrator!");break}e.message.includes("timeout")&&(t=L.global.t("axios.Network request timeout!")),e.message.includes("Network")&&(t=window.navigator.onLine?L.global.t("axios.Server exception!"):L.global.t("axios.You are disconnected!")),we({type:"error",message:t})}function xn(e){e.loading&&ae.count>0&&ae.count--,ae.count===0&&(ae.target.close(),ae.target=null)}function Qi(e){const t=xr(e);e.cancelToken=e.cancelToken||new Qt.CancelToken(n=>{ve.has(t)||ve.set(t,n)})}function yt(e){const t=xr(e);ve.has(t)&&(ve.get(t)(t),ve.delete(t))}function xr(e){let{data:t}=e;const{url:n,method:r,params:a,headers:o}=e;return typeof t=="string"&&(t=JSON.parse(t)),[n,r,o&&o.batoken?o.batoken:"",o&&o["ba-user-token"]?o["ba-user-token"]:"",JSON.stringify(a),JSON.stringify(t)].join("&")}function Zi(e){e.component("Icon",Lo);const t=Vn;for(const n in t)e.component(`el-icon-${t[n].name}`,t[n])}function el(e){const t=document.createElement("link");t.rel="stylesheet",t.href=e,t.crossOrigin="anonymous",document.getElementsByTagName("head")[0].appendChild(t)}function tl(e){const t=document.createElement("script");t.src=e,document.body.appendChild(t)}function nl(){typeof q.currentRoute.value.meta.title=="string"&&X(()=>{let e="";q.currentRoute.value.meta.title.indexOf("pagesTitle.")===-1?e=q.currentRoute.value.meta.title:e=L.global.t(q.currentRoute.value.meta.title);const t=bs(),n=Ve();t.value=`${e}${n.siteName?" - "+n.siteName:""}`})}function Cr(e){return/^(https?|ftp|mailto|tel):/.test(e)}const Xl=(e,t)=>(...n)=>{window.lazy&&clearTimeout(window.lazy),window.lazy=window.setTimeout(()=>{e(...n)},t)},Ql=(e,t,n)=>{for(const r in e)if(e[r][t]==n)return r;return!1},Zl=e=>{e&&e.resetFields&&e.resetFields()},rl=e=>{if(typeof e=="object"){const t=[];for(const n in e)t.push({label:n+": "+e[n],children:rl(e[n])});return t}else return[]},ue=(e="")=>e?/^\/admin/.test(e):!!/^\/admin/.test(Dr()),ec=e=>{const t=e.split("/");return t[t.length-1]},al=e=>{const t=Dr(),n=Po();return!!(n.state.authNode.has(t)&&n.state.authNode.get(t).some(r=>r==t+"/"+e))},ol=(e,t="")=>{const n=Ve();if(t||(t=n.cdnUrl?n.cdnUrl:me()),!e)return t;const r=new RegExp(/^http(s)?:\/\//),a=new RegExp(/^((?:[a-z]+:)?\/\/|data:image\/)(.*)/i);return!t||r.test(e)||a.test(e)?e:t+e},Dr=()=>{let e=q.currentRoute.value.path;return e=="/"&&(e=ca(window.location.hash,"#")),e.indexOf("?")!==-1&&(e=e.replace(/\?.*/,"")),e},sl=(e,t)=>{if(!e)return!1;const n=Ve(),r=n.upload.mimetype.toLowerCase().split(","),a=e.substring(e.lastIndexOf(".")+1).toLowerCase();if(n.upload.mimetype==="*"||r.includes(a)||r.includes("."+a))return!0;if(t){const o=t.toLowerCase().split("/");if(r.includes(o[0]+"/*")||r.includes(t))return!0}return!1},tc=(e,t="")=>{typeof e=="string"&&(e=e==""?[]:e.split(","));for(const n in e)e[n]=ol(e[n],t);return e},il=(e=null,t="yyyy-mm-dd hh:MM:ss")=>{if(e=="none")return L.global.t("None");e||(e=Number(new Date)),e.toString().length===10&&(e=+e*1e3);const n=new Date(e);let r;const a={"y+":n.getFullYear().toString(),"m+":(n.getMonth()+1).toString(),"d+":n.getDate().toString(),"h+":n.getHours().toString(),"M+":n.getMinutes().toString(),"s+":n.getSeconds().toString()};for(const o in a)r=new RegExp("("+o+")").exec(t),r&&(t=t.replace(r[1],r[1].length==1?a[o]:ll(a[o],r[1].length,"0")));return t},ll=(e,t,n=" ")=>{if(e.length>=t)return e;const r=t-e.length;let a=Math.ceil(r/n.length);for(;a>>=1;)n+=n,a===1&&(n+=n);return n.slice(0,r)+e},nc=()=>{const t=new Date().getHours();let n="";return t<5?n=L.global.t("utils.Late at night, pay attention to your body!"):t<9?n=L.global.t("utils.good morning!")+L.global.t("utils.welcome back"):t<12?n=L.global.t("utils.Good morning!")+L.global.t("utils.welcome back"):t<14?n=L.global.t("utils.Good noon!")+L.global.t("utils.welcome back"):t<18?n=L.global.t("utils.good afternoon")+L.global.t("utils.welcome back"):t<24?n=L.global.t("utils.Good evening")+L.global.t("utils.welcome back"):n=L.global.t("utils.Hello!")+L.global.t("utils.welcome back"),n},Cn=["//at.alicdn.com/t/font_3135462_5axiswmtpj.css"],Dn=[];function cl(){Cn.length>0&&Cn.map(e=>{el(e)}),Dn.length>0&&Dn.map(e=>{tl(e)})}function ul(e){const t=[],n=document.styleSheets;for(const r in n)n[r].href&&n[r].href.indexOf(e)>-1&&t.push(n[r]);return t}function fl(e){var r;const t=[],n=document.styleSheets;{const a=me();for(const o in n)n[o].href&&((r=n[o].href)==null?void 0:r.indexOf(a))===0&&t.push(n[o]);return t}}function rc(){return new Promise((e,t)=>{X(()=>{let n=[];const r=document.getElementById("local-icon");r!=null&&r.dataset.iconName&&(n=(r==null?void 0:r.dataset.iconName).split(",")),n.length>0?e(n):t("No Local Icons")})})}function ac(){return new Promise((e,t)=>{X(()=>{const n=[],r=fl();for(const a in r){const o=r[a].cssRules;for(const s in o)if(!(!o[s].selectorText||o[s].selectorText.indexOf(".fa-")!==0)&&/^\.fa-(.*)::before$/g.test(o[s].selectorText))if(o[s].selectorText.indexOf(", ")>-1){const i=o[s].selectorText.split(", ");n.push(`${i[0].substring(1,i[0].length).replace(/\:\:before/gi,"")}`)}else n.push(`${o[s].selectorText.substring(1,o[s].selectorText.length).replace(/\:\:before/gi,"")}`)}n.length>0?e(n):t("No AwesomeIcon style sheet")})})}function oc(){return new Promise((e,t)=>{X(()=>{const n=[],r=ul("at.alicdn.com");for(const a in r){const o=r[a].cssRules;for(const s in o)o[s].selectorText&&/^\.icon-(.*)::before$/g.test(o[s].selectorText)&&n.push(`${o[s].selectorText.substring(1,o[s].selectorText.length).replace(/\:\:before/gi,"")}`)}n.length>0?e(n):t("No Iconfont style sheet")})})}function sc(){return new Promise((e,t)=>{X(()=>{const n=[],r=Vn;for(const a in r)n.push(`el-icon-${r[a].name}`);n.length>0?e(n):t("No ElementPlus Icons")})})}var C=(e=>(e[e.Waiting=0]="Waiting",e[e.Connecting=1]="Connecting",e[e.Executing=2]="Executing",e[e.Success=3]="Success",e[e.Failed=4]="Failed",e[e.Unknown=5]="Unknown",e))(C||{});const dl=ke("terminal",()=>{const e=Me({show:!1,showDot:!1,taskList:[],packageManager:"pnpm",showPackageManagerDialog:!1,showConfig:!1,automaticCleanupTask:"1",port:"8000"});function t(){for(const b in e.taskList)(e.taskList[b].status==C.Connecting||e.taskList[b].status==C.Executing)&&(e.taskList[b].status=C.Unknown)}function n(b=!e.show){e.show=b,b&&r(!1)}function r(b=!e.showDot){e.showDot=b}function a(b=!e.showConfig){n(!b),e.showConfig=b}function o(b=!e.showPackageManagerDialog){n(!b),e.showPackageManagerDialog=b}function s(b){e.packageManager=b}function i(b){e.port=b}function c(b){e.automaticCleanupTask=b}function l(b,_){e.taskList[b].status=_,(_==C.Failed||_==C.Unknown)&&e.taskList[b].blockOnFailure&&h(b,!0)}function d(b){if(typeof e.taskList[b].callback!="function")return;const _=e.taskList[b].status;_==C.Failed||_==C.Unknown?e.taskList[b].callback(C.Failed):_==C.Success&&e.taskList[b].callback(C.Success)}function h(b,_=!e.taskList[b].showMessage){e.taskList[b].showMessage=_}function E(b,_){e.show||r(!0),e.taskList[b].message=e.taskList[b].message.concat(_),X(()=>{j(e.taskList[b].uuid)})}function v(b,_=!0,A="",I=()=>{}){if(e.show||r(!0),e.taskList=e.taskList.concat({uuid:Nr(),createTime:il(),status:C.Waiting,command:b,message:[],showMessage:!1,blockOnFailure:_,extend:A,callback:I}),parseInt(e.automaticCleanupTask)===1&&F(),e.show===!1){for(const se in e.taskList)if(e.taskList[se].status==C.Failed||e.taskList[se].status==C.Unknown){we({type:"error",message:L.global.t("terminal.Newly added tasks will never start because they are blocked by failed tasks")});break}}k()}function u(b,_=!0,A="",I=()=>{}){v(b+"."+e.packageManager,_,A,I)}function m(b){e.taskList[b].status!=C.Connecting&&e.taskList[b].status!=C.Executing&&e.taskList.splice(b,1),k()}function k(){let b=null;for(const _ in e.taskList){if(e.taskList[_].status==C.Waiting){b=parseInt(_);break}if(e.taskList[_].status==C.Connecting||e.taskList[_].status==C.Executing)break;if(e.taskList[_].status!=C.Success&&(e.taskList[_].status==C.Failed||e.taskList[_].status==C.Unknown)){if(e.taskList[_].blockOnFailure)break;continue}}b!==null&&(l(b,C.Connecting),O(b))}function O(b){window.eventSource=new EventSource(Yi(e.taskList[b].command,e.taskList[b].uuid,e.taskList[b].extend)),window.eventSource.onmessage=function(_){const A=JSON.parse(_.data);if(!A||!A.data)return;const I=S(A.uuid);I!==!1&&(A.data=="command-exec-error"?(l(I,C.Failed),window.eventSource.close(),d(I),k()):A.data=="command-exec-completed"?(window.eventSource.close(),e.taskList[I].status!=C.Success&&l(I,C.Failed),d(I),k()):A.data=="command-link-success"?l(I,C.Executing):A.data=="command-exec-success"?l(I,C.Success):E(I,A.data))},window.eventSource.onerror=function(){window.eventSource.close();const _=T(b);_!==!1&&(l(_,C.Failed),d(_))}}function P(b){e.taskList[b].message=[],l(b,C.Waiting),k()}function F(){e.taskList=e.taskList.filter(b=>b.status!=C.Success)}function S(b){for(const _ in e.taskList)if(e.taskList[_].uuid==b)return parseInt(_);return!1}function T(b){if(e.taskList[b])return b;{let _=-1;for(const A in e.taskList)(e.taskList[A].status==C.Connecting||e.taskList[A].status==C.Executing)&&(_=parseInt(A));return _===-1?!1:_}}function j(b){const _=document.querySelector(".exec-message-"+b);_&&_.scrollHeight&&(_.scrollTop=_.scrollHeight)}return{state:e,init:t,toggle:n,toggleDot:r,setTaskStatus:l,setTaskShowMessage:h,addTaskMessage:E,addTask:v,addTaskPM:u,delTask:m,startTask:k,retryTask:P,clearSuccessTask:F,togglePackageManagerDialog:o,toggleConfigDialog:a,changePackageManager:s,changePort:i,changeAutomaticCleanupTask:c}},{persist:{key:_o,paths:["state.showDot","state.taskList","state.automaticCleanupTask"]}}),ml=Ut({__name:"App",setup(e){const t=Te(),n=ua(),r=dl(),{getLocaleMessage:a}=Ba(),o=a(t.lang.defaultLang);return $t(()=>{cl(),r.init()}),fe(()=>n.path,()=>{nl()}),(s,i)=>{const c=Et("router-view"),l=Et("el-config-provider");return wt(),fa(l,{locale:Bn(o)},{default:da(()=>[be(c)]),_:1},8,["locale"])}}});function gl(e){return{all:e=e||new Map,on:function(t,n){var r=e.get(t);r?r.push(n):e.set(t,[n])},off:function(t,n){var r=e.get(t);r&&(n?r.splice(r.indexOf(n)>>>0,1):e.set(t,[]))},emit:function(t,n){var r=e.get(t);r&&r.slice().map(function(a){a(n)}),(r=e.get("*"))&&r.slice().map(function(a){a(t,n)})}}}function pl(e){return typeof e=="object"&&e!==null}function In(e,t){return e=pl(e)?e:Object.create(null),new Proxy(e,{get(n,r,a){return r==="key"?Reflect.get(n,r,a):Reflect.get(n,r,a)||Reflect.get(t,r,a)}})}function hl(e,t){return t.reduce((n,r)=>n==null?void 0:n[r],e)}function bl(e,t,n){return t.slice(0,-1).reduce((r,a)=>/^(__proto__)$/.test(a)?{}:r[a]=r[a]||{},e)[t[t.length-1]]=n,e}function _l(e,t){return t.reduce((n,r)=>{const a=r.split(".");return bl(n,a,hl(e,a))},{})}function Mn(e,{storage:t,serializer:n,key:r,debug:a}){try{const o=t==null?void 0:t.getItem(r);o&&e.$patch(n==null?void 0:n.deserialize(o))}catch(o){a&&console.error(o)}}function jn(e,{storage:t,serializer:n,key:r,paths:a,debug:o}){try{const s=Array.isArray(a)?_l(e,a):e;t.setItem(r,n.serialize(s))}catch(s){o&&console.error(s)}}function yl(e={}){return t=>{const{auto:n=!1}=e,{options:{persist:r=n},store:a}=t;if(!r)return;const o=(Array.isArray(r)?r.map(s=>In(s,e)):[In(r,e)]).map(({storage:s=localStorage,beforeRestore:i=null,afterRestore:c=null,serializer:l={serialize:JSON.stringify,deserialize:JSON.parse},key:d=a.$id,paths:h=null,debug:E=!1})=>{var v;return{storage:s,beforeRestore:i,afterRestore:c,serializer:l,key:((v=e.key)!=null?v:u=>u)(d),paths:h,debug:E}});a.$persist=()=>{o.forEach(s=>{jn(a.$state,s)})},a.$hydrate=({runHooks:s=!0}={})=>{o.forEach(i=>{const{beforeRestore:c,afterRestore:l}=i;s&&(c==null||c(t)),Mn(a,i),s&&(l==null||l(t))})},o.forEach(s=>{const{beforeRestore:i,afterRestore:c}=s;i==null||i(t),Mn(a,s),c==null||c(t),a.$subscribe((l,d)=>{jn(d,s)},{detached:!0})})}}var vl=yl();const Ir=ma();Ir.use(vl);class wl{constructor(t){pe(this,"el");pe(this,"scroll",t=>{this.el.clientWidth>=this.el.scrollWidth||(this.el.scrollLeft+=t.deltaY?t.deltaY:t.detail&&t.detail!==0?t.detail:-t.wheelDelta)});this.el=t,this.handleWheelEvent()}handleWheelEvent(){let t="";"onmousewheel"in this.el?t="mousewheel":"onwheel"in this.el?t="wheel":"attachEvent"in window?t="onmousewheel":t="DOMMouseScroll",this.el.addEventListener(t,this.scroll,{passive:!0})}}function El(e){kl(e),Ll(e),Sl(e),Ol(e),Tl(e)}function kl(e){e.directive("auth",{mounted(t,n){if(!n.value)return!1;al(n.value)||t.parentNode.removeChild(t)}})}function Tl(e){e.directive("tableLateralDrag",{created(t){new wl(t.querySelector(".el-table__body-wrapper .el-scrollbar .el-scrollbar__wrap"))}})}function Ol(e){e.directive("blur",{mounted(t){xt(t,"focus",()=>t.blur())}})}function Sl(e){e.directive("zoom",{mounted(t,n){if(!n.value)return!1;const r=ga(n.value)?[n.value,".el-dialog__body",!1,!0]:n.value;r[1]=r[1]?r[1]:".el-dialog__body",r[2]=typeof r[2]>"u"?!1:r[2],r[3]=typeof r[3]>"u"?!0:r[3],X(()=>{const a=document.querySelector(r[1]),o=document.querySelector(r[0]),s=document.createElement("div");s.className="zoom-handle",s.onmouseenter=()=>{s.onmousedown=i=>{const c=i.clientX,l=i.clientY,d=a.offsetWidth,h=a.offsetHeight,E=o.offsetWidth,v=o.offsetHeight;document.onmousemove=u=>{u.preventDefault();const m=d+(u.clientX-c)*2,k=h+(u.clientY-l);if(a.style.width=`${m}px`,a.style.height=`${k}px`,r[2]){const O=v+(u.clientY-l);o.style.height=`${O}px`}if(r[3]){const O=E+(u.clientX-c)*2;o.style.width=`${O}px`}},document.onmouseup=function(){document.onmousemove=null,document.onmouseup=null}}},o.appendChild(s)})}})}function Ll(e){e.directive("drag",{mounted(t,n){if(!n.value)return!1;const r=document.querySelector(n.value[0]),a=document.querySelector(n.value[1]);if(!a||!r)return!1;a.onmouseover=()=>a.style.cursor="move";function o(i,c){const l=c==="pc"?i.clientX-a.offsetLeft:i.touches[0].clientX-a.offsetLeft,d=c==="pc"?i.clientY-a.offsetTop:i.touches[0].clientY-a.offsetTop,h=document.body.clientWidth,E=document.body.clientHeight||document.documentElement.clientHeight,v=r.offsetWidth,u=r.offsetHeight,m=r.offsetLeft,k=h-r.offsetLeft-v,O=r.offsetTop,P=E-r.offsetTop-u;let F=getComputedStyle(r).left,S=getComputedStyle(r).top;return F=+F.replace(/\px/g,""),S=+S.replace(/\px/g,""),{disX:l,disY:d,minDragDomLeft:m,maxDragDomLeft:k,minDragDomTop:O,maxDragDomTop:P,styL:F,styT:S}}function s(i,c,l){const{disX:d,disY:h,minDragDomLeft:E,maxDragDomLeft:v,minDragDomTop:u,maxDragDomTop:m,styL:k,styT:O}=l;let P=c==="pc"?i.clientX-d:i.touches[0].clientX-d,F=c==="pc"?i.clientY-h:i.touches[0].clientY-h;-P>E?P=-E:P>v&&(P=v),-F>u?F=-u:F>m&&(F=m),r.style.cssText+=`;left:${P+k}px;top:${F+O}px;`}a.onmousedown=i=>{const c=o(i,"pc");document.onmousemove=l=>{s(l,"pc",c)},document.onmouseup=()=>{document.onmousemove=null,document.onmouseup=null}},a.ontouchstart=i=>{const c=o(i,"app");document.ontouchmove=l=>{s(l,"app",c)},document.ontouchend=()=>{document.ontouchmove=null,document.ontouchend=null}}}})}async function Pl(){const e=pa(ml);e.use(Ir),await wo(e),e.use(q),e.use(ha),El(e),Zi(e),e.mount("#app"),e.config.globalProperties.eventBus=gl()}Pl();export{po as A,xl as B,x as C,sc as D,ac as E,oc as F,rc as G,Ql as H,Hl as I,ol as J,tc as K,il as L,al as M,Xl as N,Yl as O,ec as P,Ul as Q,Vl as R,ho as S,$l as T,rl as U,Jl as V,nc as W,Nl as X,Zl as Y,Oo as _,Ye as a,Nr as b,Gl as c,Ba as d,Dl as e,H as f,ql as g,Ml as h,L as i,Il as j,Ve as k,Po as l,xt as m,jl as n,wl as o,dl as p,Wl as q,q as r,Bl as s,C as t,Te as u,Kl as v,Cl as w,zl as x,Nt as y,ue as z};
