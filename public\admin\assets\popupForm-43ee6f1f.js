import{h as U,w as C,ar as j,r as x,F as q,q as d,ab as c,o as u,O as f,P as s,a7 as b,k as G,Z as h,V as y,z as e,m as V,l as w,p as m,_ as L,a3 as W,a5 as $}from"./vue-9f0739d1.js";import{f as H,d as O,b as K,_ as Z}from"./index-572ce0f1.js";import{F as R}from"./index-f8da5656.js";import{b as J}from"./validate-eddfbf9e.js";import"./index-1d626fdc.js";import"./index-d8b6d591.js";function M(){return H({url:"/admin/auth.Rule/index",method:"get"})}const Q={class:"title"},X=U({__name:"popupForm",setup(Y,{expose:F}){const _=C(),g=C(),t=j("baTable"),{t:l}=O(),a=x({treeKey:K(),defaultCheckedKeys:[],menuRules:[]}),S=x({name:[J({name:"required",title:l("auth.group.Group name")})],auth:[{required:!0,validator:(n,o,r)=>v().length<=0?r(new Error(l("Please select field",{field:l("auth.group.jurisdiction")}))):r()}],pid:[{validator:(n,o,r)=>o&&parseInt(o)==parseInt(t.form.items.id)?r(new Error(l("auth.group.The parent group cannot be the group itself"))):r(),trigger:"blur"}]});M().then(n=>{a.menuRules=n.data.list});const v=()=>g.value.getCheckedKeys().concat(g.value.getHalfCheckedKeys()),I=(n,o)=>{if(o.isLeaf)return"";let r=!0;for(const p in o.childNodes)o.childNodes[p].isLeaf||(r=!1);return r?"penultimate-node":""};return F({getCheckeds:v}),q(()=>t.form.items.rules,()=>{if(t.form.items.rules&&t.form.items.rules.length)if(t.form.items.rules.includes("*")){let n=[];for(const o in a.menuRules)n.push(a.menuRules[o].id);a.defaultCheckedKeys=n}else a.defaultCheckedKeys=t.form.items.rules;else a.defaultCheckedKeys=[];a.treeKey=K()}),(n,o)=>{const r=d("el-input"),p=d("el-form-item"),N=d("el-tree"),z=d("el-form"),D=d("el-scrollbar"),k=d("el-button"),E=d("el-dialog"),P=c("drag"),T=c("zoom"),A=c("loading"),B=c("blur");return u(),f(E,{class:"ba-operate-dialog","close-on-click-modal":!1,"model-value":["Add","Edit"].includes(e(t).form.operate),onClose:e(t).toggleForm,"destroy-on-close":!0},{header:s(()=>[b((u(),G("div",Q,[h(y(e(t).form.operate?e(l)(e(t).form.operate):""),1)])),[[P,[".ba-operate-dialog",".el-dialog__header"]],[T,".ba-operate-dialog"]])]),footer:s(()=>[V("div",{style:w("width: calc(100% - "+e(t).form.labelWidth/1.8+"px)")},[m(k,{onClick:o[4]||(o[4]=i=>e(t).toggleForm(""))},{default:s(()=>[h(y(e(l)("Cancel")),1)]),_:1}),b((u(),f(k,{loading:e(t).form.submitLoading,onClick:o[5]||(o[5]=i=>e(t).onSubmit(_.value)),type:"primary"},{default:s(()=>[h(y(e(t).form.operateIds&&e(t).form.operateIds.length>1?e(l)("Save and edit next item"):e(l)("Save")),1)]),_:1},8,["loading"])),[[B]])],4)]),default:s(()=>[b((u(),f(D,{class:"ba-table-form-scrollbar"},{default:s(()=>[V("div",{class:L(["ba-operate-form","ba-"+e(t).form.operate+"-form"]),style:w("width: calc(100% - "+e(t).form.labelWidth/2+"px)")},[e(t).form.loading?$("",!0):(u(),f(z,{key:0,ref_key:"formRef",ref:_,onKeyup:o[3]||(o[3]=W(i=>e(t).onSubmit(_.value),["enter"])),model:e(t).form.items,"label-position":"right","label-width":e(t).form.labelWidth+"px",rules:S},{default:s(()=>[m(R,{label:e(l)("auth.group.Parent group"),modelValue:e(t).form.items.pid,"onUpdate:modelValue":o[0]||(o[0]=i=>e(t).form.items.pid=i),type:"remoteSelect",prop:"pid","input-attr":{params:{isTree:!0},field:"name","remote-url":e(t).api.actionUrl.get("index"),placeholder:e(l)("Click select")}},null,8,["label","modelValue","input-attr"]),m(p,{prop:"name",label:e(l)("auth.group.Group name")},{default:s(()=>[m(r,{modelValue:e(t).form.items.name,"onUpdate:modelValue":o[1]||(o[1]=i=>e(t).form.items.name=i),type:"string",placeholder:e(l)("Please input field",{field:e(l)("auth.group.Group name")})},null,8,["modelValue","placeholder"])]),_:1},8,["label"]),m(p,{prop:"auth",label:e(l)("auth.group.jurisdiction")},{default:s(()=>[(u(),f(N,{ref_key:"treeRef",ref:g,key:a.treeKey,"default-checked-keys":a.defaultCheckedKeys,"default-expand-all":!0,"show-checkbox":"","node-key":"id",props:{children:"children",label:"title",class:I},data:a.menuRules},null,8,["default-checked-keys","props","data"]))]),_:1},8,["label"]),m(R,{label:e(l)("State"),modelValue:e(t).form.items.status,"onUpdate:modelValue":o[2]||(o[2]=i=>e(t).form.items.status=i),type:"radio",data:{content:{0:e(l)("Disable"),1:e(l)("Enable")},childrenAttr:{border:!0}}},null,8,["label","modelValue","data"])]),_:1},8,["model","label-width","rules"]))],6)]),_:1})),[[A,e(t).form.loading]])]),_:1},8,["model-value","onClose"])}}});const se=Z(X,[["__scopeId","data-v-700a7135"]]);export{se as default};
