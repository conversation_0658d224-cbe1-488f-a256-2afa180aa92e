import{u as N,k as ze,B as re,_ as R,l as P,m as Ce,n as Ye,o as Ze,d as xe,S as Ge,p as we,t as z,q as Me,v as Je,a as Le,e as Qe,A as he,r as De,w as Ve,x as et,b as me,y as tt}from"./index-572ce0f1.js";import{h as A,U as j,z as e,q as p,o as i,k as S,a5 as I,l as ne,V as T,O as x,_ as D,Y as le,X as E,P as a,p as n,m,N as te,w as J,r as O,j as Q,E as X,a6 as ce,n as ee,D as Ie,a7 as K,a8 as Ee,a9 as Te,aa as ot,a4 as Ne,W as se,Z as $,$ as Pe,a0 as qe,F as Ue,ab as nt,ac as lt,ad as at,ae as st,af as Se,ag as We,ah as rt,ai as ct,aj as ut,i as it}from"./vue-9f0739d1.js";import{c as dt,o as Ae,g as He,r as _e,s as mt,h as _t}from"./router-e3edf306.js";import{S as ae,L as G}from"./storage-19836658.js";import{s as ke,m as pt}from"./layout-9c3294eb.js";import{I as ft}from"./index-1d626fdc.js";import{t as vt,a as gt,i as yt}from"./useDark-3f3e4e6b.js";import{F as ie}from"./index-f8da5656.js";import"./index-d8b6d591.js";const bt="/admin/assets/logo-8000aeec.png",ht={class:"layout-logo"},Ct={key:0,class:"logo-img",src:bt,alt:"logo"},kt=A({__name:"logo",setup(g){j(r=>({adf9ae48:e(t).layout.layoutMode!="Streamline"?e(t).getColorVal("menuTopBarBackground"):"transparent"}));const t=N(),o=ze(),c=function(){t.layout.shrink&&!t.layout.menuCollapse&&dt(),t.setLayout("menuCollapse",!t.layout.menuCollapse),ae.set(re,{layoutMode:t.layout.layoutMode,menuCollapse:t.layout.menuCollapse}),setTimeout(()=>{ke()},350)};return(r,_)=>{const f=p("Icon");return i(),S("div",ht,[e(t).layout.menuCollapse?I("",!0):(i(),S("img",Ct)),e(t).layout.menuCollapse?I("",!0):(i(),S("div",{key:1,style:ne({color:e(t).getColorVal("menuActiveColor")}),class:"website-name"},T(e(o).siteName),5)),e(t).layout.layoutMode!="Streamline"?(i(),x(f,{key:2,onClick:c,name:e(t).layout.menuCollapse?"fa fa-indent":"fa fa-dedent",class:D([e(t).layout.menuCollapse?"unfold":"","fold"]),color:e(t).getColorVal("menuActiveColor"),size:"18"},null,8,["name","class","color"])):I("",!0)])}}});const Oe=R(kt,[["__scopeId","data-v-38fd2478"]]),xt=A({__name:"menuTree",props:{menus:{default:()=>[]}},setup(g){const t=g;j(r=>({20955865:e(o).getColorVal("menuActiveBackground")}));const o=N(),c=r=>{var _;["Streamline","Double"].includes(o.layout.layoutMode)&&((_=r.children)!=null&&_.length)&&Ae(r.children[0])};return(r,_)=>{const f=p("Icon"),C=p("menu-tree",!0),b=p("el-sub-menu"),u=p("el-menu-item");return i(!0),S(E,null,le(t.menus,d=>(i(),S(E,null,[d.children&&d.children.length>0?(i(),x(b,{onClick:v=>c(d),index:d.path,key:d.path},{title:a(()=>{var v,l,s,V;return[n(f,{color:e(o).getColorVal("menuColor"),name:(v=d.meta)!=null&&v.icon?(l=d.meta)==null?void 0:l.icon:e(o).layout.menuDefaultIcon},null,8,["color","name"]),m("span",null,T((s=d.meta)!=null&&s.title?(V=d.meta)==null?void 0:V.title:r.$t("noTitle")),1)]}),default:a(()=>[n(C,{menus:d.children},null,8,["menus"])]),_:2},1032,["onClick","index"])):(i(),x(u,{index:d.path,key:d.path,onClick:v=>e(Ae)(d)},{default:a(()=>{var v,l,s,V;return[n(f,{color:e(o).getColorVal("menuColor"),name:(v=d.meta)!=null&&v.icon?(l=d.meta)==null?void 0:l.icon:e(o).layout.menuDefaultIcon},null,8,["color","name"]),m("span",null,T((s=d.meta)!=null&&s.title?(V=d.meta)==null?void 0:V.title:r.$t("noTitle")),1)]}),_:2},1032,["index","onClick"]))],64))),256)}}});const pe=R(xt,[["__scopeId","data-v-f95e303f"]]),wt=A({__name:"menuVertical",setup(g){j(u=>({"4ac75b46":f.value,fc15b866:e(t).getColorVal("menuBackground"),"5a437bdc":e(t).getColorVal("menuColor"),"1fda8a2c":e(t).getColorVal("menuActiveColor")}));const t=N(),o=P(),c=te(),r=J(),_=O({defaultActive:""}),f=Q(()=>{let u=0;return t.layout.menuShowTopBar&&(u=50),t.layout.layoutMode=="Default"?"calc(100vh - "+(32+u)+"px)":"calc(100vh - "+u+"px)"}),C=u=>{_.defaultActive=u.path},b=()=>{ee(()=>{var d;let u=document.querySelector(".el-menu.layouts-menu-vertical li.is-active");if(!u)return!1;(d=r.value)==null||d.setScrollTop(u.offsetTop)})};return X(()=>{C(c),b()}),ce(u=>{C(u)}),(u,d)=>{const v=p("el-menu"),l=p("el-scrollbar");return i(),x(l,{ref_key:"verticalMenusRef",ref:r,class:"vertical-menus-scrollbar"},{default:a(()=>[n(v,{class:"layouts-menu-vertical","collapse-transition":!1,"unique-opened":e(t).layout.menuUniqueOpened,"default-active":_.defaultActive,collapse:e(t).layout.menuCollapse},{default:a(()=>[n(pe,{menus:e(o).state.tabsViewRoutes},null,8,["menus"])]),_:1},8,["unique-opened","default-active","collapse"])]),_:1},512)}}});const Be=(g,t)=>{for(let o=0;o<t.length;o++){const c=t[o];if(c.path==g||c.children&&c.children.length>0&&Be(g,c.children))return c}return!1},Tt=A({__name:"menuVerticalChildren",setup(g){j(u=>({"676a4508":f.value,"5173dffa":e(t).getColorVal("menuBackground"),"79ac0c1c":e(t).getColorVal("menuColor"),"74a724b6":e(t).getColorVal("menuActiveColor")}));const t=N(),o=P(),c=te(),r=J(),_=O({defaultActive:"",routeChildren:[]}),f=Q(()=>{let u=0;return t.layout.menuShowTopBar&&(u=50),t.layout.layoutMode=="Default"?"calc(100vh - "+(32+u)+"px)":"calc(100vh - "+u+"px)"}),C=u=>{let d=Be(u.path,o.state.tabsViewRoutes);d?(_.defaultActive=u.path,d.children&&d.children.length>0?_.routeChildren=d.children:_.routeChildren=[d]):_.routeChildren||(_.routeChildren=o.state.tabsViewRoutes)},b=()=>{ee(()=>{var d;let u=document.querySelector(".el-menu.layouts-menu-vertical-children li.is-active");if(!u)return!1;(d=r.value)==null||d.setScrollTop(u.offsetTop)})};return X(()=>{C(c),b()}),ce(u=>{C(u)}),(u,d)=>{const v=p("el-menu"),l=p("el-scrollbar");return i(),x(l,{ref_key:"verticalMenusRef",ref:r,class:"children-vertical-menus-scrollbar"},{default:a(()=>[n(v,{class:"layouts-menu-vertical-children","collapse-transition":!1,"unique-opened":e(t).layout.menuUniqueOpened,"default-active":_.defaultActive,collapse:e(t).layout.menuCollapse},{default:a(()=>[_.routeChildren.length>0?(i(),x(pe,{key:0,menus:_.routeChildren},null,8,["menus"])):I("",!0)]),_:1},8,["unique-opened","default-active","collapse"])]),_:1},512)}}});const St=A({name:"layout/aside",__name:"aside",setup(g){j(r=>({"12d2a223":c.value}));const t=N(),o=P(),c=Q(()=>t.menuWidth());return(r,_)=>{const f=p("el-aside");return e(o).state.tabFullScreen?I("",!0):(i(),x(f,{key:0,class:D("layout-aside-"+e(t).layout.layoutMode+" "+(e(t).layout.shrink?"shrink":""))},{default:a(()=>[e(t).layout.menuShowTopBar?(i(),x(Oe,{key:0})):I("",!0),e(t).layout.layoutMode=="Double"?(i(),x(Tt,{key:1})):(i(),x(wt,{key:2}))]),_:1},8,["class"]))}}});const $e=R(St,[["__scopeId","data-v-be977e32"]]);function Ke(){if(!Ie())throw new Error("useCurrentInstance() can only be used inside setup() or functional components!");const{appContext:g}=Ie();return{proxy:g.config.globalProperties}}const Bt={class:"el-dropdown-menu"},$t=["onClick"],Mt=A({__name:"index",props:{width:{default:150},items:{default:()=>[]}},emits:["contextmenuItemClick"],setup(g,{expose:t,emit:o}){const c=g,r=O({show:!1,axis:{x:0,y:0},menu:void 0,arrowAxis:10}),_=(b,u)=>{r.menu=b,r.axis=u,r.show=!0},f=b=>{b.disabled||(b.menu=ot(r.menu),o("contextmenuItemClick",b))},C=()=>{r.show=!1};return t({onShowContextmenu:_,onHideContextmenu:C}),X(()=>{Ce(document,"click",C)}),(b,u)=>{const d=p("Icon");return i(),x(Te,{name:"el-zoom-in-center"},{default:a(()=>[K((i(),S("div",{class:"el-popper is-pure is-light el-dropdown__popper ba-contextmenu",style:ne(`top: ${r.axis.y+5}px;left: ${r.axis.x-14}px;width:${c.width}px`),key:Math.random(),"aria-hidden":"false","data-popper-placement":"bottom"},[m("ul",Bt,[(i(!0),S(E,null,le(c.items,(v,l)=>(i(),S("li",{key:l,class:D(["el-dropdown-menu__item",v.disabled?"is-disabled":""]),tabindex:"-1",onClick:s=>f(v)},[n(d,{size:"12",name:v.icon},null,8,["name"]),m("span",null,T(v.label),1)],10,$t))),128))]),m("span",{class:"el-popper__arrow",style:ne({left:`${r.arrowAxis}px`})},null,4)],4)),[[Ee,r.show]])]),_:1})}}});const Vt=R(Mt,[["__scopeId","data-v-b68cb97f"]]),It=["onClick","onContextmenu"],At=A({__name:"tabs",setup(g){j(h=>({"5fbf0dc5":e(c).getColorVal("headerBarTabColor"),"12b2839f":e(c).getColorVal("headerBarTabActiveColor"),"9ee2b184":e(c).layout.layoutMode=="Default"?"none":e(c).getColorVal("headerBarBackground")}));const t=te(),o=Ne(),c=N(),r=P(),{proxy:_}=Ke(),f=J(),C=Ye(),b=J(),u=O({contextmenuItems:[{name:"refresh",label:"重新加载",icon:"fa fa-refresh"},{name:"close",label:"关闭标签",icon:"fa fa-times"},{name:"fullScreen",label:"当前标签全屏",icon:"el-icon-FullScreen"},{name:"closeOther",label:"关闭其他标签",icon:"fa fa-minus"},{name:"closeAll",label:"关闭全部标签",icon:"fa fa-stop"}]}),d=O({width:"0",transform:"translateX(0px)"}),v=h=>{o.push(h)},l=(h,M)=>{u.contextmenuItems[0].disabled=t.path!==h.path,u.contextmenuItems[4].disabled=u.contextmenuItems[3].disabled=r.state.tabsView.length==1;const{clientX:B,clientY:W}=M;b.value.onShowContextmenu(h,{x:B,y:W})},s=function(h){if(!h)return!1;d.width=h.clientWidth+"px",d.transform=`translateX(${h.offsetLeft}px)`;let M=h.offsetLeft+h.clientWidth-f.value.clientWidth;h.offsetLeft<f.value.scrollLeft?f.value.scrollTo(h.offsetLeft,0):M>f.value.scrollLeft&&f.value.scrollTo(M,0)},V=()=>{const h=r.state.tabsView.slice(-1)[0];h?o.push(h):o.push("/admin")},q=h=>{var M;r.closeTab(h),_.eventBus.emit("onTabViewClose",h),((M=r.state.activeRoute)==null?void 0:M.path)===h.path?V():(r.setActiveRoute(r.state.activeRoute),ee(()=>{s(C.value[r.state.activeIndex])})),b.value.onHideContextmenu()},w=h=>{var M;r.closeTabs(h),r.setActiveRoute(h),((M=r.state.activeRoute)==null?void 0:M.path)!==t.path&&o.push(h.path)},U=h=>{let M=He(r.state.tabsViewRoutes);if(M&&M.path==h.path)return w(h);r.closeTabs(!1),M&&_e(M.path)},Z=async h=>{const{name:M,menu:B}=h;if(B)switch(M){case"refresh":_.eventBus.emit("onTabViewRefresh",B);break;case"close":q(B);break;case"closeOther":w(B);break;case"closeAll":U(B);break;case"fullScreen":t.path!==(B==null?void 0:B.path)&&o.push(B==null?void 0:B.path),r.setFullScreen(!0);break}},F=function(h){r.addTab(h),r.setActiveRoute(h),ee(()=>{s(C.value[r.state.activeIndex])})};return ce(async h=>{F(h)}),X(()=>{F(o.currentRoute.value),new Ze(f.value)}),(h,M)=>{const B=p("Icon");return i(),S(E,null,[m("div",{class:"nav-tabs",ref_key:"tabScrollbarRef",ref:f},[(i(!0),S(E,null,le(e(r).state.tabsView,(W,oe)=>(i(),S("div",{onClick:L=>v(W),onContextmenu:se(L=>l(W,L),["prevent"]),class:D(["ba-nav-tab",e(r).state.activeIndex==oe?"active":""]),ref_for:!0,ref:e(C).set,key:oe},[$(T(W.meta.title)+" ",1),n(Te,{onAfterLeave:M[0]||(M[0]=L=>s(e(C)[e(r).state.activeIndex])),name:"el-fade-in"},{default:a(()=>[K(n(B,{class:"close-icon",onClick:se(L=>q(W),["stop"]),size:"15",name:"el-icon-Close"},null,8,["onClick"]),[[Ee,e(r).state.tabsView.length>1]])]),_:2},1024)],42,It))),128)),m("div",{style:ne(d),class:"nav-tabs-active-box"},null,4)],512),n(Vt,{ref_key:"contextmenuRef",ref:b,items:u.contextmenuItems,onContextmenuItemClick:Z},null,8,["items"])],64)}}});const je=R(At,[["__scopeId","data-v-1e4ba09f"]]),Re=[["requestFullscreen","exitFullscreen","fullscreenElement","fullscreenEnabled","fullscreenchange","fullscreenerror"],["webkitRequestFullscreen","webkitExitFullscreen","webkitFullscreenElement","webkitFullscreenEnabled","webkitfullscreenchange","webkitfullscreenerror"],["webkitRequestFullScreen","webkitCancelFullScreen","webkitCurrentFullScreenElement","webkitCancelFullScreen","webkitfullscreenchange","webkitfullscreenerror"],["mozRequestFullScreen","mozCancelFullScreen","mozFullScreenElement","mozFullScreenEnabled","mozfullscreenchange","mozfullscreenerror"],["msRequestFullscreen","msExitFullscreen","msFullscreenElement","msFullscreenEnabled","MSFullscreenChange","MSFullscreenError"]],Y=(()=>{if(typeof document>"u")return!1;const g=Re[0],t={};for(const o of Re)if((o==null?void 0:o[1])in document){for(const[r,_]of o.entries())t[g[r]]=_;return t}return!1})(),Fe={change:Y.fullscreenchange,error:Y.fullscreenerror};let H={request(g=document.documentElement,t){return new Promise((o,c)=>{const r=()=>{H.off("change",r),o()};H.on("change",r);const _=g[Y.requestFullscreen](t);_ instanceof Promise&&_.then(r).catch(c)})},exit(){return new Promise((g,t)=>{if(!H.isFullscreen){g();return}const o=()=>{H.off("change",o),g()};H.on("change",o);const c=document[Y.exitFullscreen]();c instanceof Promise&&c.then(o).catch(t)})},toggle(g,t){return H.isFullscreen?H.exit():H.request(g,t)},onchange(g){H.on("change",g)},onerror(g){H.on("error",g)},on(g,t){const o=Fe[g];o&&document.addEventListener(o,t,!1)},off(g,t){const o=Fe[g];o&&document.removeEventListener(o,t,!1)},raw:Y};Object.defineProperties(H,{isFullscreen:{get:()=>!!document[Y.fullscreenElement]},element:{enumerable:!0,get:()=>document[Y.fullscreenElement]??void 0},isEnabled:{enumerable:!0,get:()=>!!document[Y.fullscreenEnabled]}});Y||(H={isEnabled:!1});const de=H;const Rt={},Ft={class:"theme-toggle-content"},zt={class:"switch"},Lt={class:"switch-action"};function Dt(g,t){const o=p("Icon");return i(),S("div",Ft,[m("div",zt,[m("div",Lt,[n(o,{name:"local-dark",color:"#f2f2f2",size:"13px",class:"switch-icon dark-icon"}),n(o,{name:"local-light",color:"#303133",size:"13px",class:"switch-icon light-icon"})])])])}const Et=R(Rt,[["render",Dt],["__scopeId","data-v-047bd707"]]),fe=g=>(Pe("data-v-bac0a267"),g=g(),qe(),g),Nt={class:"layout-config-drawer"},Pt={class:"layout-mode-styles-box"},qt={class:"layout-mode-box-style"},Ut=fe(()=>m("div",{class:"layout-mode-style-box"},[m("div",{class:"layout-mode-style-aside"}),m("div",{class:"layout-mode-style-container-box"},[m("div",{class:"layout-mode-style-header"}),m("div",{class:"layout-mode-style-container"})])],-1)),Wt={class:"layout-mode-style-name"},Ht=fe(()=>m("div",{class:"layout-mode-style-box"},[m("div",{class:"layout-mode-style-aside"}),m("div",{class:"layout-mode-style-container-box"},[m("div",{class:"layout-mode-style-header"}),m("div",{class:"layout-mode-style-container"})])],-1)),Ot={class:"layout-mode-style-name"},Kt=fe(()=>m("div",{class:"layout-mode-style-box"},[m("div",{class:"layout-mode-style-container-box"},[m("div",{class:"layout-mode-style-header"}),m("div",{class:"layout-mode-style-container"})])],-1)),jt={class:"layout-mode-style-name"},Xt=fe(()=>m("div",{class:"layout-mode-style-box"},[m("div",{class:"layout-mode-style-aside"}),m("div",{class:"layout-mode-style-container-box"},[m("div",{class:"layout-mode-style-header"}),m("div",{class:"layout-mode-style-container"})])],-1)),Yt={class:"layout-mode-style-name"},Zt={class:"layout-config-global"},Gt={class:"layout-config-aside"},Jt={class:"layout-config-aside"},Qt={class:"ba-center"},eo=A({__name:"config",setup(g){const{t}=xe(),o=N(),c=P(),r=Ne(),_=(v,l)=>{o.setLayout(l,v)},f=(v,l)=>{if(v===null)return;const s=o.layout[l];o.layout.isDark?s[1]=v:s[0]=v,o.setLayout(l,s)},C=v=>{ae.set(re,{layoutMode:v,menuCollapse:o.layout.menuCollapse}),o.setLayoutMode(v)},b=(v,l)=>{o.setLayout(l,v);const s=c.state.tabsViewRoutes;c.setTabsViewRoutes([]),setTimeout(()=>{c.setTabsViewRoutes(s)},200)},u=()=>{o.setLayout("showDrawer",!1)},d=()=>{G.remove(Ge),ae.remove(re),r.go(0)};return(v,l)=>{const s=p("el-divider"),V=p("el-col"),q=p("el-row"),w=p("el-form-item"),U=p("el-option"),Z=p("el-select"),F=p("el-color-picker"),h=p("el-switch"),M=p("el-input"),B=p("el-button"),W=p("el-popconfirm"),oe=p("el-form"),L=p("el-scrollbar"),y=p("el-drawer");return i(),S("div",Nt,[n(y,{"model-value":e(o).layout.showDrawer,title:e(t)("layouts.Layout configuration"),size:"310px",onClose:u},{default:a(()=>[n(L,{class:"layout-mode-style-scrollbar"},{default:a(()=>[n(oe,{ref:"formRef",model:e(o).layout},{default:a(()=>[m("div",Pt,[n(s,{"border-style":"dashed"},{default:a(()=>[$(T(e(t)("layouts.Layout mode")),1)]),_:1}),m("div",qt,[n(q,{class:"layout-mode-box-style-row",gutter:10},{default:a(()=>[n(V,{span:12},{default:a(()=>[m("div",{onClick:l[0]||(l[0]=k=>C("Default")),class:D(["layout-mode-style default",e(o).layout.layoutMode=="Default"?"active":""])},[Ut,m("div",Wt,T(e(t)("layouts.default")),1)],2)]),_:1}),n(V,{span:12},{default:a(()=>[m("div",{onClick:l[1]||(l[1]=k=>C("Classic")),class:D(["layout-mode-style classic",e(o).layout.layoutMode=="Classic"?"active":""])},[Ht,m("div",Ot,T(e(t)("layouts.classic")),1)],2)]),_:1})]),_:1}),n(q,{gutter:10},{default:a(()=>[n(V,{span:12},{default:a(()=>[m("div",{onClick:l[2]||(l[2]=k=>C("Streamline")),class:D(["layout-mode-style streamline",e(o).layout.layoutMode=="Streamline"?"active":""])},[Kt,m("div",jt,T(e(t)("layouts.Single column")),1)],2)]),_:1}),n(V,{span:12},{default:a(()=>[m("div",{onClick:l[3]||(l[3]=k=>C("Double")),class:D(["layout-mode-style double",e(o).layout.layoutMode=="Double"?"active":""])},[Xt,m("div",Yt,T(e(t)("layouts.Double column")),1)],2)]),_:1})]),_:1})]),n(s,{"border-style":"dashed"},{default:a(()=>[$(T(e(t)("layouts.overall situation")),1)]),_:1}),m("div",Zt,[n(w,{size:"large",label:e(t)("layouts.Dark mode")},{default:a(()=>[n(Et,{onClick:l[4]||(l[4]=k=>e(vt)())})]),_:1},8,["label"]),n(w,{label:e(t)("layouts.Background page switching animation")},{default:a(()=>[n(Z,{onChange:l[5]||(l[5]=k=>_(k,"mainAnimation")),"model-value":e(o).layout.mainAnimation,placeholder:e(t)("layouts.Please select an animation name")},{default:a(()=>[n(U,{label:"slide-right",value:"slide-right"}),n(U,{label:"slide-left",value:"slide-left"}),n(U,{label:"el-fade-in-linear",value:"el-fade-in-linear"}),n(U,{label:"el-fade-in",value:"el-fade-in"}),n(U,{label:"el-zoom-in-center",value:"el-zoom-in-center"}),n(U,{label:"el-zoom-in-top",value:"el-zoom-in-top"}),n(U,{label:"el-zoom-in-bottom",value:"el-zoom-in-bottom"})]),_:1},8,["model-value","placeholder"])]),_:1},8,["label"])]),n(s,{"border-style":"dashed"},{default:a(()=>[$(T(e(t)("layouts.sidebar")),1)]),_:1}),m("div",Gt,[n(w,{label:e(t)("layouts.Side menu bar background color")},{default:a(()=>[n(F,{onChange:l[6]||(l[6]=k=>f(k,"menuBackground")),"model-value":e(o).getColorVal("menuBackground")},null,8,["model-value"])]),_:1},8,["label"]),n(w,{label:e(t)("layouts.Side menu text color")},{default:a(()=>[n(F,{onChange:l[7]||(l[7]=k=>f(k,"menuColor")),"model-value":e(o).getColorVal("menuColor")},null,8,["model-value"])]),_:1},8,["label"]),n(w,{label:e(t)("layouts.Side menu active item background color")},{default:a(()=>[n(F,{onChange:l[8]||(l[8]=k=>f(k,"menuActiveBackground")),"model-value":e(o).getColorVal("menuActiveBackground")},null,8,["model-value"])]),_:1},8,["label"]),n(w,{label:e(t)("layouts.Side menu active item text color")},{default:a(()=>[n(F,{onChange:l[9]||(l[9]=k=>f(k,"menuActiveColor")),"model-value":e(o).getColorVal("menuActiveColor")},null,8,["model-value"])]),_:1},8,["label"]),n(w,{label:e(t)("layouts.Show side menu top bar (logo bar)")},{default:a(()=>[n(h,{onChange:l[10]||(l[10]=k=>_(k,"menuShowTopBar")),"model-value":e(o).layout.menuShowTopBar},null,8,["model-value"])]),_:1},8,["label"]),n(w,{label:e(t)("layouts.Side menu top bar background color")},{default:a(()=>[n(F,{onChange:l[11]||(l[11]=k=>f(k,"menuTopBarBackground")),"model-value":e(o).getColorVal("menuTopBarBackground")},null,8,["model-value"])]),_:1},8,["label"]),n(w,{label:e(t)("layouts.Side menu width (when expanded)")},{default:a(()=>[n(M,{onInput:l[12]||(l[12]=k=>_(k,"menuWidth")),type:"number",step:10,"model-value":e(o).layout.menuWidth},{append:a(()=>[$("px")]),_:1},8,["model-value"])]),_:1},8,["label"]),n(w,{label:e(t)("layouts.Side menu default icon")},{default:a(()=>[n(ft,{onChange:l[13]||(l[13]=k=>b(k,"menuDefaultIcon")),"model-value":e(o).layout.menuDefaultIcon},null,8,["model-value"])]),_:1},8,["label"]),n(w,{label:e(t)("layouts.Side menu horizontal collapse")},{default:a(()=>[n(h,{onChange:l[14]||(l[14]=k=>_(k,"menuCollapse")),"model-value":e(o).layout.menuCollapse},null,8,["model-value"])]),_:1},8,["label"]),n(w,{label:e(t)("layouts.Side menu accordion")},{default:a(()=>[n(h,{onChange:l[15]||(l[15]=k=>_(k,"menuUniqueOpened")),"model-value":e(o).layout.menuUniqueOpened},null,8,["model-value"])]),_:1},8,["label"])]),n(s,{"border-style":"dashed"},{default:a(()=>[$(T(e(t)("layouts.Top bar")),1)]),_:1}),m("div",Jt,[n(w,{label:e(t)("layouts.Top bar background color")},{default:a(()=>[n(F,{onChange:l[16]||(l[16]=k=>f(k,"headerBarBackground")),"model-value":e(o).getColorVal("headerBarBackground")},null,8,["model-value"])]),_:1},8,["label"]),n(w,{label:e(t)("layouts.Top bar text color")},{default:a(()=>[n(F,{onChange:l[17]||(l[17]=k=>f(k,"headerBarTabColor")),"model-value":e(o).getColorVal("headerBarTabColor")},null,8,["model-value"])]),_:1},8,["label"]),n(w,{label:e(t)("layouts.Background color when hovering over the top bar")},{default:a(()=>[n(F,{onChange:l[18]||(l[18]=k=>f(k,"headerBarHoverBackground")),"model-value":e(o).getColorVal("headerBarHoverBackground")},null,8,["model-value"])]),_:1},8,["label"]),n(w,{label:e(t)("layouts.Top bar menu active item background color")},{default:a(()=>[n(F,{onChange:l[19]||(l[19]=k=>f(k,"headerBarTabActiveBackground")),"model-value":e(o).getColorVal("headerBarTabActiveBackground")},null,8,["model-value"])]),_:1},8,["label"]),n(w,{label:e(t)("layouts.Top bar menu active item text color")},{default:a(()=>[n(F,{onChange:l[20]||(l[20]=k=>f(k,"headerBarTabActiveColor")),"model-value":e(o).getColorVal("headerBarTabActiveColor")},null,8,["model-value"])]),_:1},8,["label"])]),n(W,{onConfirm:d,title:e(t)("layouts.Are you sure you want to restore all configurations to the default values?")},{reference:a(()=>[m("div",Qt,[n(B,{class:"w80",type:"info"},{default:a(()=>[$(T(e(t)("layouts.Restore default")),1)]),_:1})])]),_:1},8,["title"])])]),_:1},8,["model"])]),_:1})]),_:1},8,["model-value","title"])])}}});const to=R(eo,[["__scopeId","data-v-bac0a267"]]),oo={class:"command"},no={class:"task-opt"},lo=["onClick"],ao={class:"indent-2"},so={class:"package-manager-dialog-footer"},ro={class:"config-buttons"},co=A({__name:"index",setup(g){const{t}=xe(),o=we(),c=O({terminalWarning:"",port:o.state.port,menuExpand:document.documentElement.clientWidth>1840}),r=Q(()=>{let l=o.state.port==""?"80":o.state.port;return l=="8000"?"php think run":"php think run -p "+l}),_=Q(()=>{let l=o.state.port==""?"":":"+o.state.port;return"http://localhost"+l+" "+t("terminal.or")+" http://"+t("terminal.Site domain name")+l}),f=l=>{Me({port:l}).then(s=>{s.code==1?(o.changePort(l),v()):c.port=o.state.port}).catch(()=>{c.port=o.state.port})},C=l=>{let s=t("terminal.unknown"),V="info";switch(l){case z.Waiting:s=t("terminal.Waiting for execution"),V="info";break;case z.Connecting:s=t("terminal.Connecting"),V="warning";break;case z.Executing:s=t("terminal.Executing"),V="warning";break;case z.Success:s=t("terminal.Successful execution"),V="success";break;case z.Failed:s=t("terminal.Execution failed"),V="danger";break;case z.Unknown:s=t("terminal.Unknown execution result"),V="danger";break}return{statusText:s,statusType:V}},b=()=>{at.confirm(t("terminal.Are you sure you want to republish?"),t("Reminder"),{confirmButtonText:t("Confirm"),cancelButtonText:t("Cancel"),type:"warning"}).then(()=>{o.addTaskPM("web-build")})},u=l=>{Me({manager:l}).then(s=>{s.code==1&&o.changePackageManager(l)}),o.togglePackageManagerDialog(!1)},d=l=>{c.terminalWarning=l},v=()=>{Je()!=o.state.port?d(t("terminal.The current terminal is not running under the installation service, and some commands may not be executed")):d("")};return Ue(()=>o.state.port,l=>{l!=c.port&&(c.port=l,v())}),X(()=>{v()}),(l,s)=>{const V=p("el-alert"),q=p("el-tag"),w=p("el-button"),U=p("Icon"),Z=p("el-card"),F=p("el-timeline-item"),h=p("el-timeline"),M=p("el-empty"),B=p("el-button-group"),W=p("el-dialog"),oe=p("el-form"),L=nt("blur");return i(),S(E,null,[n(W,lt(l.$attrs,{modelValue:e(o).state.show,"onUpdate:modelValue":s[7]||(s[7]=y=>e(o).state.show=y),title:e(t)("terminal.Terminal"),class:"ba-terminal-dialog","append-to-body":!0}),{default:a(()=>[c.terminalWarning?(i(),x(V,{key:0,class:"terminal-warning-alert",title:c.terminalWarning,type:"error"},null,8,["title"])):I("",!0),e(o).state.taskList.length?(i(),x(h,{key:1},{default:a(()=>[(i(!0),S(E,null,le(e(o).state.taskList,(y,k)=>(i(),x(F,{key:k,class:D(["task-item","task-status-"+y.status]),type:C(y.status).statusType,center:"",timestamp:y.createTime,placement:"top"},{default:a(()=>[n(Z,null,{default:a(()=>[m("div",null,[n(q,{type:C(y.status).statusType},{default:a(()=>[$(T(C(y.status).statusText),1)]),_:2},1032,["type"]),(y.status==e(z).Failed||y.status==e(z).Unknown)&&y.blockOnFailure?(i(),x(q,{key:0,class:"block-on-failure-tag",type:"warning"},{default:a(()=>[$(T(e(t)("terminal.Failure to execute this command will block the execution of the queue")),1)]),_:1})):I("",!0),y.status==e(z).Executing||y.status==e(z).Connecting?(i(),x(q,{key:1,class:"block-on-failure-tag",type:"danger"},{default:a(()=>[$(T(e(t)("terminal.Do not refresh the browser")),1)]),_:1})):I("",!0),m("span",oo,T(y.command),1),m("div",no,[y.status==e(z).Failed||y.status==e(z).Unknown?K((i(),x(w,{key:0,title:e(t)("Retry"),size:"small",type:"warning",icon:"el-icon-RefreshRight",circle:"",onClick:ue=>e(o).retryTask(k)},null,8,["title","onClick"])),[[L]]):I("",!0),K(n(w,{onClick:ue=>e(o).delTask(k),title:e(t)("Delete"),size:"small",type:"danger",icon:"el-icon-Delete",circle:""},null,8,["onClick","title"]),[[L]])])]),y.status!=e(z).Waiting?(i(),S(E,{key:0},[y.status!=e(z).Connecting&&y.status!=e(z).Executing?(i(),S("div",{key:0,onClick:ue=>e(o).setTaskShowMessage(k),class:"toggle-message-display"},[m("span",null,T(e(t)("terminal.Command run log")),1),n(U,{name:y.showMessage?"el-icon-ArrowUp":"el-icon-ArrowDown",size:"16",color:"#909399"},null,8,["name"])],8,lo)):I("",!0),y.status==e(z).Connecting||y.status==e(z).Executing||y.status>e(z).Executing&&y.showMessage?(i(),S("div",{key:1,class:D(["exec-message","exec-message-"+y.uuid])},[(i(!0),S(E,null,le(y.message,(ue,Xe)=>(i(),S("pre",{key:Xe,class:"message-item"},T(ue),1))),128))],2)):I("",!0)],64)):I("",!0)]),_:2},1024)]),_:2},1032,["class","type","timestamp"]))),128))]),_:1})):(i(),x(M,{key:2,"image-size":80,description:e(t)("terminal.No mission yet")},null,8,["description"])),n(B,null,{default:a(()=>[K((i(),x(w,{class:"terminal-menu-item",icon:"el-icon-MagicStick",onClick:s[0]||(s[0]=y=>e(o).addTaskPM("test",!1))},{default:a(()=>[$(T(e(t)("terminal.Test command")),1)]),_:1})),[[L]]),K((i(),x(w,{class:"terminal-menu-item",icon:"el-icon-Download",onClick:s[1]||(s[1]=y=>e(o).addTaskPM("web-install"))},{default:a(()=>[$(T(e(t)("terminal.Install dependent packages")),1)]),_:1})),[[L]]),K((i(),x(w,{class:"terminal-menu-item",icon:"el-icon-Sell",onClick:s[2]||(s[2]=y=>b())},{default:a(()=>[$(T(e(t)("terminal.Republish")),1)]),_:1})),[[L]]),c.menuExpand?(i(),S(E,{key:1},[K((i(),x(w,{class:"terminal-menu-item",icon:"el-icon-Delete",onClick:s[4]||(s[4]=y=>e(o).clearSuccessTask())},{default:a(()=>[$(T(e(t)("terminal.Clean up task list")),1)]),_:1})),[[L]]),K((i(),x(w,{class:"terminal-menu-item",icon:"el-icon-Switch",onClick:s[5]||(s[5]=y=>e(o).togglePackageManagerDialog(!0))},{default:a(()=>[$(T(e(t)("terminal.Package manager"))+" "+T(e(o).state.packageManager.toUpperCase()),1)]),_:1})),[[L]]),K((i(),x(w,{class:"terminal-menu-item",icon:"el-icon-Tools",onClick:s[6]||(s[6]=y=>e(o).toggleConfigDialog())},{default:a(()=>[$(T(e(t)("terminal.Terminal settings")),1)]),_:1})),[[L]])],64)):K((i(),x(w,{key:0,class:"terminal-menu-item",icon:"el-icon-Expand",onClick:s[3]||(s[3]=y=>c.menuExpand=!0)},null,512)),[[L]])]),_:1})]),_:1},16,["modelValue","title"]),n(W,{onClose:s[14]||(s[14]=y=>e(o).togglePackageManagerDialog(!1)),"model-value":e(o).state.showPackageManagerDialog,class:"ba-terminal-dialog",title:e(t)("terminal.Please select package manager"),center:""},{footer:a(()=>[m("div",so,[n(w,{onClick:s[8]||(s[8]=y=>u("npm"))},{default:a(()=>[$("npm")]),_:1}),n(w,{onClick:s[9]||(s[9]=y=>u("cnpm"))},{default:a(()=>[$("cnpm")]),_:1}),n(w,{onClick:s[10]||(s[10]=y=>u("pnpm"))},{default:a(()=>[$("pnpm")]),_:1}),n(w,{onClick:s[11]||(s[11]=y=>u("yarn"))},{default:a(()=>[$("yarn")]),_:1}),n(w,{onClick:s[12]||(s[12]=y=>u("ni"))},{default:a(()=>[$("ni")]),_:1}),n(w,{onClick:s[13]||(s[13]=y=>u("none"))},{default:a(()=>[$(T(e(t)("terminal.I want to execute the command manually")),1)]),_:1})])]),default:a(()=>[m("div",ao,T(e(t)("terminal.Switch package manager title")),1)]),_:1},8,["model-value","title"]),n(W,{onClose:s[19]||(s[19]=y=>e(o).toggleConfigDialog(!1)),"model-value":e(o).state.showConfig,class:"ba-terminal-dialog",title:e(t)("terminal.Terminal settings")},{default:a(()=>[n(oe,{"label-position":"top"},{default:a(()=>[n(ie,{label:e(t)("terminal.Install service port"),modelValue:c.port,"onUpdate:modelValue":s[15]||(s[15]=y=>c.port=y),type:"number","input-attr":{onChange:f},placeholder:e(t)("terminal.The port number to start the installation service (this port needs to be opened for external network access)")},null,8,["label","modelValue","input-attr","placeholder"]),n(ie,{label:e(t)("terminal.Installation service startup command"),modelValue:r.value,"onUpdate:modelValue":s[16]||(s[16]=y=>r.value=y),type:"string","input-attr":{disabled:!0},attr:{blockHelp:e(t)("terminal.Please execute this command to start the service (add Su under Linux)")}},null,8,["label","modelValue","attr"]),n(ie,{label:e(t)("terminal.Installation service URL"),modelValue:_.value,"onUpdate:modelValue":s[17]||(s[17]=y=>_.value=y),type:"string","input-attr":{disabled:!0},attr:{blockHelp:e(t)("terminal.Please access the site through the installation service URL (except in debug mode)")}},null,8,["label","modelValue","attr"])]),_:1}),n(ie,{label:e(t)("terminal.Clean up successful tasks when starting a new task"),"model-value":e(o).state.automaticCleanupTask,type:"radio",data:{content:{0:e(t)("Disable"),1:e(t)("Enable")},childrenAttr:{border:!0}},"input-attr":{onChange:e(o).changeAutomaticCleanupTask}},null,8,["label","model-value","data","input-attr"]),m("div",ro,[n(w,{onClick:s[18]||(s[18]=y=>e(o).toggleConfigDialog(!1))},{default:a(()=>[$(T(e(t)("terminal.Back to terminal")),1)]),_:1})])]),_:1},8,["model-value","title"])],64)}}});const uo=R(co,[["__scopeId","data-v-30ed251f"]]),io={class:"nav-menu-item"},mo=["src"],_o={class:"admin-name"},po={class:"admin-info-base"},fo=["src"],vo={class:"admin-info-other"},go={class:"admin-info-name"},yo={class:"admin-info-lasttime"},bo={class:"admin-info-footer"},ho=A({__name:"navMenus",setup(g){j(v=>({"3bc3a01a":e(c).getColorVal("headerBarBackground"),"179fc60c":e(c).getColorVal("headerBarTabColor"),"71709e4c":e(c).getColorVal("headerBarHoverBackground")}));const{t}=xe(),o=Le(),c=N(),r=we(),_=O({isFullScreen:!1,currentNavMenu:"",showLayoutDrawer:!1}),f=(v,l)=>{_.currentNavMenu=v?l:""},C=()=>{if(!de.isEnabled)return st.warning(t("layouts.Full screen is not supported")),!1;de.toggle(),de.onchange(()=>{_.isFullScreen=de.isFullscreen})},b=()=>{_e({name:"routine/adminInfo"})},u=()=>{gt().then(()=>{G.remove(he),De.go(0)})},d=v=>{if(v=="storage"||v=="all"){const l=G.get(he),s=G.get(Ve);if(ae.clear(),G.clear(),G.set(he,l),G.set(Ve,s),v=="storage")return}et(v).then(()=>{})};return(v,l)=>{const s=p("Icon"),V=p("router-link"),q=p("el-dropdown-item"),w=p("el-dropdown-menu"),U=p("el-dropdown"),Z=p("el-badge"),F=p("el-avatar"),h=p("el-button"),M=p("el-popover");return i(),S("div",{class:D(["nav-menus",e(c).layout.layoutMode])},[n(V,{class:"h100",target:"_blank",title:e(t)("Home"),to:"/"},{default:a(()=>[m("div",io,[n(s,{color:e(c).getColorVal("headerBarTabColor"),class:"nav-menu-icon",name:"el-icon-Monitor",size:"18"},null,8,["color"])])]),_:1},8,["title"]),n(U,{onVisibleChange:l[0]||(l[0]=B=>f(B,"lang")),class:"h100",size:"large","hide-timeout":50,placement:"bottom",trigger:"click","hide-on-click":!0},{dropdown:a(()=>[n(w,{class:"dropdown-menu-box"},{default:a(()=>[(i(!0),S(E,null,le(e(c).lang.langArray,B=>(i(),x(q,{key:B.name,onClick:W=>e(Qe)(B.name)},{default:a(()=>[$(T(B.value),1)]),_:2},1032,["onClick"]))),128))]),_:1})]),default:a(()=>[m("div",{class:D(["nav-menu-item pt2",_.currentNavMenu=="lang"?"hover":""])},[n(s,{color:e(c).getColorVal("headerBarTabColor"),class:"nav-menu-icon",name:"local-lang",size:"18"},null,8,["color"])],2)]),_:1}),m("div",{onClick:C,class:D(["nav-menu-item",_.isFullScreen?"hover":""])},[_.isFullScreen?(i(),x(s,{key:0,color:e(c).getColorVal("headerBarTabColor"),class:"nav-menu-icon",name:"local-full-screen-cancel",size:"18"},null,8,["color"])):(i(),x(s,{key:1,color:e(c).getColorVal("headerBarTabColor"),class:"nav-menu-icon",name:"el-icon-FullScreen",size:"18"},null,8,["color"]))],2),e(o).super?(i(),S("div",{key:0,onClick:l[1]||(l[1]=B=>e(r).toggle()),class:"nav-menu-item pt2"},[n(Z,{"is-dot":e(r).state.showDot},{default:a(()=>[n(s,{color:e(c).getColorVal("headerBarTabColor"),class:"nav-menu-icon",name:"local-terminal",size:"26"},null,8,["color"])]),_:1},8,["is-dot"])])):I("",!0),n(U,{onVisibleChange:l[5]||(l[5]=B=>f(B,"clear")),class:"h100",size:"large","hide-timeout":50,placement:"bottom",trigger:"click","hide-on-click":!0},{dropdown:a(()=>[n(w,{class:"dropdown-menu-box"},{default:a(()=>[n(q,{onClick:l[2]||(l[2]=B=>d("tp"))},{default:a(()=>[$(T(e(t)("utils.Clean up system cache")),1)]),_:1}),n(q,{onClick:l[3]||(l[3]=B=>d("storage"))},{default:a(()=>[$(T(e(t)("utils.Clean up browser cache")),1)]),_:1}),n(q,{onClick:l[4]||(l[4]=B=>d("all")),divided:""},{default:a(()=>[$(T(e(t)("utils.Clean up all cache")),1)]),_:1})]),_:1})]),default:a(()=>[m("div",{class:D(["nav-menu-item",_.currentNavMenu=="clear"?"hover":""])},[n(s,{color:e(c).getColorVal("headerBarTabColor"),class:"nav-menu-icon",name:"el-icon-Delete",size:"18"},null,8,["color"])],2)]),_:1}),n(M,{onShow:l[6]||(l[6]=B=>f(!0,"adminInfo")),onHide:l[7]||(l[7]=B=>f(!1,"adminInfo")),placement:"bottom-end","hide-after":0,width:260,trigger:"click","popper-class":"admin-info-box"},{reference:a(()=>[m("div",{class:D(["admin-info",_.currentNavMenu=="adminInfo"?"hover":""])},[n(F,{size:25,fit:"fill"},{default:a(()=>[m("img",{src:e(o).avatar,alt:""},null,8,mo)]),_:1}),m("div",_o,T(e(o).nickname),1)],2)]),default:a(()=>[m("div",null,[m("div",po,[n(F,{size:70,fit:"fill"},{default:a(()=>[m("img",{src:e(o).avatar,alt:""},null,8,fo)]),_:1}),m("div",vo,[m("div",go,T(e(o).nickname),1),m("div",yo,T(e(o).last_login_time),1)])]),m("div",bo,[n(h,{onClick:b,type:"primary",plain:""},{default:a(()=>[$(T(e(t)("layouts.personal data")),1)]),_:1}),n(h,{onClick:u,type:"danger",plain:""},{default:a(()=>[$(T(e(t)("layouts.cancellation")),1)]),_:1})])])]),_:1}),m("div",{onClick:l[8]||(l[8]=B=>e(c).setLayout("showDrawer",!0)),class:"nav-menu-item"},[n(s,{color:e(c).getColorVal("headerBarTabColor"),class:"nav-menu-icon",name:"fa fa-cogs",size:"18"},null,8,["color"])]),n(to),n(uo)],2)}}});const ve=R(ho,[["__scopeId","data-v-cf7ce49c"]]),Co={class:"nav-bar"},ko=A({__name:"default",setup(g){j(o=>({"341e91f0":e(t).getColorVal("headerBarTabColor"),"7d0afb8a":e(t).getColorVal("headerBarTabActiveColor"),"5eccd556":e(t).getColorVal("headerBarTabActiveBackground")}));const t=N();return(o,c)=>(i(),S("div",Co,[n(je),n(ve)]))}});const xo=R(ko,[["__scopeId","data-v-8d90b156"]]),wo={class:"nav-bar"},To={key:0,class:"unfold"},So=A({__name:"classic",setup(g){j(c=>({"88b2895c":e(t).getColorVal("headerBarBackground"),"6a10a632":e(t).getColorVal("headerBarTabColor"),d50d5368:e(t).getColorVal("headerBarTabActiveColor"),"4c5473d8":e(t).getColorVal("headerBarHoverBackground"),a7c0b85a:e(t).getColorVal("headerBarTabActiveBackground")}));const t=N(),o=()=>{mt("ba-aside-menu-shade",()=>{t.setLayout("menuCollapse",!0)}),t.setLayout("menuCollapse",!1)};return(c,r)=>{const _=p("Icon");return i(),S("div",wo,[e(t).layout.shrink&&e(t).layout.menuCollapse?(i(),S("div",To,[n(_,{onClick:o,name:"fa fa-indent",color:e(t).getColorVal("menuActiveColor"),size:"18"},null,8,["color"])])):I("",!0),e(t).layout.shrink?I("",!0):(i(),x(je,{key:1})),n(ve)])}}});const Bo=R(So,[["__scopeId","data-v-af750839"]]),$o={class:"layouts-menu-horizontal"},Mo={key:0,class:"menu-horizontal-logo"},Vo=A({__name:"menuHorizontal",setup(g){j(u=>({"229d1d8d":e(o).getColorVal("headerBarHoverBackground"),"33daf554":e(o).getColorVal("menuBackground"),"896b622e":e(o).getColorVal("menuColor"),"3f6959c3":e(o).getColorVal("menuActiveColor"),bc6b9a88:e(o).getColorVal("menuActiveBackground")}));const t=J(),o=N(),c=P(),r=te(),_=O({menuKey:me(),defaultActive:""}),f=Q(()=>(_.menuKey=me(),c.state.tabsViewRoutes)),C=u=>{_.defaultActive=u.path},b=()=>{ee(()=>{var d;let u=document.querySelector(".el-menu.menu-horizontal li.is-active");if(!u)return!1;(d=t.value)==null||d.setScrollTop(u.offsetTop)})};return X(()=>{C(r),b()}),ce(u=>{C(u)}),(u,d)=>{const v=p("el-menu"),l=p("el-scrollbar");return i(),S("div",$o,[e(o).layout.menuShowTopBar?(i(),S("div",Mo,[n(Oe)])):I("",!0),n(l,{ref_key:"horizontalMenusRef",ref:t,class:"horizontal-menus-scrollbar"},{default:a(()=>[(i(),x(v,{class:"menu-horizontal",mode:"horizontal","default-active":_.defaultActive,key:_.menuKey},{default:a(()=>[n(pe,{menus:f.value},null,8,["menus"])]),_:1},8,["default-active"]))]),_:1},512),n(ve)])}}});const Io=R(Vo,[["__scopeId","data-v-6aa3158a"]]),Ao={class:"layouts-menu-horizontal-double"},Ro=A({__name:"double",setup(g){j(u=>({"173d9c9f":e(o).getColorVal("menuBackground"),"62de0e00":e(o).getColorVal("menuColor"),"33a12b9a":e(o).getColorVal("menuActiveColor"),"68a22176":e(o).getColorVal("menuActiveBackground")}));const t=J(),o=N(),c=P(),r=te(),_=O({menuKey:me(),defaultActive:""}),f=Q(()=>(_.menuKey=me(),c.state.tabsViewRoutes)),C=u=>{Be(u.path,c.state.tabsViewRoutes)&&(_.defaultActive=u.path)},b=()=>{ee(()=>{var d;let u=document.querySelector(".el-menu.menu-horizontal li.is-active");if(!u)return!1;(d=t.value)==null||d.setScrollTop(u.offsetTop)})};return X(()=>{C(r),b()}),ce(u=>{C(u)}),(u,d)=>{const v=p("el-menu"),l=p("el-scrollbar");return i(),S("div",Ao,[n(l,{ref_key:"horizontalMenusRef",ref:t,class:"double-menus-scrollbar"},{default:a(()=>[(i(),x(v,{class:"menu-horizontal",mode:"horizontal","default-active":_.defaultActive,key:_.menuKey},{default:a(()=>[n(pe,{menus:f.value},null,8,["menus"])]),_:1},8,["default-active"]))]),_:1},512),n(ve)])}}});const Fo=R(Ro,[["__scopeId","data-v-df6db00e"]]),zo=A({name:"layout/header",components:{DefaultNavBar:xo,ClassicNavBar:Bo,StreamlineNavBar:Io,DoubleNavBar:Fo},__name:"header",setup(g){const t=N(),o=P();return(c,r)=>{const _=p("el-header");return e(o).state.tabFullScreen?I("",!0):(i(),x(_,{key:0,class:"layout-header"},{default:a(()=>[(i(),x(Se(e(t).layout.layoutMode+"NavBar")))]),_:1}))}}});const ge=R(zo,[["__scopeId","data-v-9014eea9"]]),Lo=A({name:"layout/main",__name:"main",setup(g){const{proxy:t}=Ke(),o=te(),c=N(),r=P(),_=J(),f=O({componentKey:o.path,keepAliveComponentNameList:[]}),C=function(b){if(b){if(f.keepAliveComponentNameList.find(d=>d===b))return;f.keepAliveComponentNameList.push(b)}};return We(()=>{t.eventBus.on("onTabViewRefresh",b=>{f.keepAliveComponentNameList=f.keepAliveComponentNameList.filter(u=>b.meta.keepalive!==u),f.componentKey="",ee(()=>{f.componentKey=b.path,C(b.meta.keepalive)})}),t.eventBus.on("onTabViewClose",b=>{f.keepAliveComponentNameList=f.keepAliveComponentNameList.filter(u=>b.meta.keepalive!==u)})}),rt(()=>{t.eventBus.off("onTabViewRefresh"),t.eventBus.off("onTabViewClose")}),X(()=>{var b,u;typeof((b=r.state.activeRoute)==null?void 0:b.meta.keepalive)=="string"&&C((u=r.state.activeRoute)==null?void 0:u.meta.keepalive)}),Ue(()=>o.path,()=>{var b,u;f.componentKey=o.path,typeof((b=r.state.activeRoute)==null?void 0:b.meta.keepalive)=="string"&&C((u=r.state.activeRoute)==null?void 0:u.meta.keepalive)}),ct("mainScrollbarRef",_),(b,u)=>{const d=p("router-view"),v=p("el-scrollbar"),l=p("el-main");return i(),x(l,{class:"layout-main"},{default:a(()=>[n(v,{class:"layout-main-scrollbar",style:ne(e(pt)()),ref_key:"mainScrollbarRef",ref:_},{default:a(()=>[n(d,null,{default:a(({Component:s})=>[n(Te,{name:e(c).layout.mainAnimation,mode:"out-in"},{default:a(()=>[(i(),x(ut,{include:f.keepAliveComponentNameList},[(i(),x(Se(s),{key:f.componentKey}))],1032,["include"]))]),_:2},1032,["name"])]),_:1})]),_:1},8,["style"])]),_:1})}}});const ye=R(Lo,[["__scopeId","data-v-9573a780"]]),Do=g=>(Pe("data-v-0aa634f1"),g=g(),qe(),g),Eo=["title","onMouseover","onMouseout"],No=["onClick"],Po=Do(()=>m("div",{class:"close-full-screen-on"},null,-1)),qo=A({__name:"closeFullScreen",setup(g){const t=P(),o=O({closeBoxTop:20});X(()=>{setTimeout(()=>{o.closeBoxTop=-30},300)});const c=()=>{o.closeBoxTop=20},r=()=>{o.closeBoxTop=-30},_=()=>{t.setFullScreen(!1)};return(f,C)=>{const b=p("Icon");return i(),S("div",{title:f.$t("layouts.Exit full screen"),onMouseover:se(c,["stop"]),onMouseout:se(r,["stop"])},[m("div",{onClick:se(_,["stop"]),class:"close-full-screen",style:ne({top:o.closeBoxTop+"px"})},[n(b,{name:"el-icon-Close"})],12,No),Po],40,Eo)}}});const be=R(qo,[["__scopeId","data-v-0aa634f1"]]),Uo=A({__name:"default",setup(g){const t=P();return(o,c)=>{const r=p("el-container");return i(),S(E,null,[n(r,{class:"layout-container"},{default:a(()=>[n($e),n(r,{class:"content-wrapper"},{default:a(()=>[n(ge),n(ye)]),_:1})]),_:1}),e(t).state.tabFullScreen?(i(),x(be,{key:0})):I("",!0)],64)}}});const Wo=R(Uo,[["__scopeId","data-v-59d252c1"]]),Ho=A({__name:"classic",setup(g){const t=P();return(o,c)=>{const r=p("el-container");return i(),S(E,null,[n(r,{class:"layout-container"},{default:a(()=>[n($e),n(r,{class:"content-wrapper"},{default:a(()=>[n(ge),n(ye)]),_:1})]),_:1}),e(t).state.tabFullScreen?(i(),x(be,{key:0})):I("",!0)],64)}}});const Oo=R(Ho,[["__scopeId","data-v-fb349671"]]),Ko=A({__name:"streamline",setup(g){const t=P();return(o,c)=>{const r=p("el-container");return i(),S(E,null,[n(r,{class:"layout-container"},{default:a(()=>[n(r,{class:"content-wrapper"},{default:a(()=>[n(ge),n(ye)]),_:1})]),_:1}),e(t).state.tabFullScreen?(i(),x(be,{key:0})):I("",!0)],64)}}});const jo=R(Ko,[["__scopeId","data-v-fcdc1bd3"]]),Xo=A({__name:"double",setup(g){const t=P();return(o,c)=>{const r=p("el-container");return i(),S(E,null,[n(r,{class:"layout-container"},{default:a(()=>[n($e),n(r,{class:"content-wrapper"},{default:a(()=>[n(ge),n(ye)]),_:1})]),_:1}),e(t).state.tabFullScreen?(i(),x(be,{key:0})):I("",!0)],64)}}});const Yo=R(Xo,[["__scopeId","data-v-d08319bd"]]),an=A({components:{Default:Wo,Classic:Oo,Streamline:jo,Double:Yo},__name:"index",setup(g){const t=we(),o=P(),c=N(),r=te(),_=ze(),f=Le(),C=O({autoMenuCollapseLock:!1});X(()=>{if(!f.token)return De.push({name:"adminLogin"});b(),ke(),Ce(window,"resize",ke)}),We(()=>{u(),Ce(window,"resize",u)});const b=()=>{yt().then(d=>{if(_.dataFill(d.data.siteConfig),t.changePort(d.data.terminal.installServicePort),t.changePackageManager(d.data.terminal.npmPackageManager),f.dataFill(d.data.adminInfo),d.data.menus){if(_t(d.data.menus),r.params.to){const l=JSON.parse(r.params.to);if(l.path!=tt.path){let s=it(l.query)?{}:l.query;_e({path:l.path,query:s});return}}let v=He(o.state.tabsViewRoutes);v&&_e(v.path)}})},u=()=>{let d={layoutMode:c.layout.layoutMode,menuCollapse:c.layout.menuCollapse},v=ae.get(re);if(v||ae.set(re,d),document.body.clientWidth<1024)C.autoMenuCollapseLock||(C.autoMenuCollapseLock=!0,c.setLayout("menuCollapse",!0)),c.setLayout("shrink",!0),c.setLayoutMode("Classic");else{C.autoMenuCollapseLock=!1;let s=v||d;c.setLayout("menuCollapse",s.menuCollapse),c.setLayout("shrink",!1),c.setLayoutMode(s.layoutMode)}};return(d,v)=>(i(),x(Se(e(c).layout.layoutMode)))}});export{an as default};
