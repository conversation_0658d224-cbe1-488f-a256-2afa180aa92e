import{h as W,w as z,ar as F,r as B,q as f,ab as u,o as m,O as d,P as s,a7 as c,k as D,Z as w,V as _,z as e,m as V,l as k,p as n,_ as N,W as b,a3 as y,a5 as h}from"./vue-9f0739d1.js";import{d as $}from"./index-572ce0f1.js";import{F as r}from"./index-f8da5656.js";import{b as i}from"./validate-eddfbf9e.js";const A={class:"title"},M=W({__name:"popupForm",setup(K){const p=z(),t=F("baTable"),{t:l}=$(),g=B({import_type:[i({name:"required",title:l("fish.wallet.import_type")})],mnemonic:[i({name:"required",title:l("fish.wallet.mnemonic")})],privatekey:[i({name:"required",title:l("fish.wallet.privatekey")})],contract_type:[i({name:"required",title:l("fish.wallet.contract_type")})],wallet_name:[i({name:"required",title:l("fish.wallet.wallet_name")})],wallet_from:[i({name:"required",title:l("fish.wallet.wallet_from")})],create_time:[i({name:"date",title:l("fish.wallet.create_time")})],update_time:[i({name:"date",title:l("fish.wallet.update_time")})]});return(T,a)=>{const C=f("el-form"),x=f("el-scrollbar"),v=f("el-button"),P=f("el-dialog"),S=u("drag"),U=u("zoom"),q=u("loading"),I=u("blur");return m(),d(P,{class:"ba-operate-dialog","close-on-click-modal":!1,"model-value":["Add","Edit"].includes(e(t).form.operate),onClose:e(t).toggleForm,width:"50%"},{header:s(()=>[c((m(),D("div",A,[w(_(e(t).form.operate?e(l)(e(t).form.operate):""),1)])),[[S,[".ba-operate-dialog",".el-dialog__header"]],[U,".ba-operate-dialog"]])]),footer:s(()=>[V("div",{style:k("width: calc(100% - "+e(t).form.labelWidth/1.8+"px)")},[n(v,{onClick:a[12]||(a[12]=o=>e(t).toggleForm())},{default:s(()=>[w(_(e(l)("Cancel")),1)]),_:1}),c((m(),d(v,{loading:e(t).form.submitLoading,onClick:a[13]||(a[13]=o=>e(t).onSubmit(p.value)),type:"primary"},{default:s(()=>[w(_(e(t).form.operateIds&&e(t).form.operateIds.length>1?e(l)("Save and edit next item"):e(l)("Save")),1)]),_:1},8,["loading"])),[[I]])],4)]),default:s(()=>[c((m(),d(x,{class:"ba-table-form-scrollbar"},{default:s(()=>[V("div",{class:N(["ba-operate-form","ba-"+e(t).form.operate+"-form"]),style:k("width: calc(100% - "+e(t).form.labelWidth/2+"px)")},[e(t).form.loading?h("",!0):(m(),d(C,{key:0,ref_key:"formRef",ref:p,onSubmit:a[10]||(a[10]=b(()=>{},["prevent"])),onKeyup:a[11]||(a[11]=y(o=>e(t).onSubmit(p.value),["enter"])),model:e(t).form.items,"label-position":"right","label-width":e(t).form.labelWidth+"px",rules:g},{default:s(()=>[n(r,{label:e(l)("fish.wallet.import_type"),type:"select",modelValue:e(t).form.items.import_type,"onUpdate:modelValue":a[0]||(a[0]=o=>e(t).form.items.import_type=o),prop:"import_type",data:{content:{mnemonic:e(l)("fish.wallet.mnemonic"),privatekey:e(l)("fish.wallet.privatekey")}},placeholder:e(l)("Please select field",{field:e(l)("fish.wallet.import_type")})},null,8,["label","modelValue","data","placeholder"]),e(t).form.items.import_type==="mnemonic"?(m(),d(r,{key:0,label:e(l)("fish.wallet.mnemonic"),type:"string",modelValue:e(t).form.items.mnemonic,"onUpdate:modelValue":a[1]||(a[1]=o=>e(t).form.items.mnemonic=o),prop:"mnemonic",placeholder:e(l)("Please input field",{field:e(l)("fish.wallet.mnemonic")})},null,8,["label","modelValue","placeholder"])):h("",!0),e(t).form.items.import_type==="privatekey"?(m(),d(r,{key:1,label:e(l)("fish.wallet.privatekey"),type:"string",modelValue:e(t).form.items.privatekey,"onUpdate:modelValue":a[2]||(a[2]=o=>e(t).form.items.privatekey=o),prop:"privatekey",placeholder:e(l)("Please input field",{field:e(l)("fish.wallet.privatekey")})},null,8,["label","modelValue","placeholder"])):h("",!0),n(r,{label:e(l)("fish.wallet.contract_type"),type:"select",modelValue:e(t).form.items.contract_type,"onUpdate:modelValue":a[3]||(a[3]=o=>e(t).form.items.contract_type=o),prop:"contract_type",data:{content:{trc:e(l)("fish.wallet.contract_type trc"),erc:e(l)("fish.wallet.contract_type erc"),bsc:e(l)("fish.wallet.contract_type bsc"),okc:e(l)("fish.wallet.contract_type okc")}},placeholder:e(l)("Please select field",{field:e(l)("fish.wallet.contract_type")})},null,8,["label","modelValue","data","placeholder"]),n(r,{label:e(l)("fish.wallet.wallet_name"),type:"string",modelValue:e(t).form.items.wallet_name,"onUpdate:modelValue":a[4]||(a[4]=o=>e(t).form.items.wallet_name=o),prop:"wallet_name","input-attr":{step:1},placeholder:e(l)("Please input field",{field:e(l)("fish.wallet.wallet_name")})},null,8,["label","modelValue","placeholder"]),n(r,{label:e(l)("fish.wallet.wallet_from"),type:"select",modelValue:e(t).form.items.wallet_from,"onUpdate:modelValue":a[5]||(a[5]=o=>e(t).form.items.wallet_from=o),prop:"wallet_from",data:{content:{Android:e(l)("fish.wallet.wallet_from Android"),Ios:e(l)("fish.wallet.wallet_from Ios"),Web:e(l)("fish.wallet.wallet_from Web")}}},null,8,["label","modelValue","data"]),n(r,{label:e(l)("fish.wallet.status"),type:"select",modelValue:e(t).form.items.status,"onUpdate:modelValue":a[6]||(a[6]=o=>e(t).form.items.status=o),prop:"status",data:{content:{0:e(l)("fish.wallet.status 0"),1:e(l)("fish.wallet.status 1")}},placeholder:e(l)("Please select field",{field:e(l)("fish.wallet.status")})},null,8,["label","modelValue","data","placeholder"]),n(r,{label:e(l)("fish.wallet.remark"),type:"textarea",modelValue:e(t).form.items.remark,"onUpdate:modelValue":a[7]||(a[7]=o=>e(t).form.items.remark=o),prop:"remark","input-attr":{rows:3},onKeyup:[a[8]||(a[8]=y(b(()=>{},["stop"]),["enter"])),a[9]||(a[9]=y(b(o=>e(t).onSubmit(p.value),["ctrl"]),["enter"]))],placeholder:e(l)("Please input field",{field:e(l)("fish.wallet.remark")})},null,8,["label","modelValue","placeholder"])]),_:1},8,["model","label-width","rules"]))],6)]),_:1})),[[q,e(t).form.loading]])]),_:1},8,["model-value","onClose"])}}});export{M as _};
