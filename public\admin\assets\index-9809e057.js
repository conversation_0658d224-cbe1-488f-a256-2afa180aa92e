import e from"./start-4ce01f80.js";import t from"./design-6ce0666f.js";import{s as r}from"./crud-d10e7b25.js";import{d as m}from"./index-572ce0f1.js";import{h as n,E as a,o,k as s,O as p,af as i,z as c}from"./vue-9f0739d1.js";import"./index-f8da5656.js";import"./index-1d626fdc.js";import"./index-d8b6d591.js";import"./validate-eddfbf9e.js";import"./log.vue_vue_type_style_index_0_lang-e99006ad.js";const g=n({name:"crud/crud",components:{Start:e,Design:t},__name:"index",setup(f){return m(),a(()=>{}),(u,_)=>(o(),s("div",null,[(o(),p(i(c(r).step)))]))}});export{g as default};
