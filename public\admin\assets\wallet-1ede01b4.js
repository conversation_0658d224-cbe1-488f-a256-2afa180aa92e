const e={id:"主键",import_type:"导入类型",mnemonic:"鱼苗助记词",privatekey:"鱼苗私钥",address:"鱼苗地址",main_balance:"主币余额",usdt_balance:"USDT余额",contract_type:"合约类型","contract_type trc":"波场链","contract_type erc":"以太链","contract_type bsc":"币安链","contract_type okc":"欧易链",wallet_name:"钱包名称",wallet_from:"钱包平台","wallet_from Android":"安卓","wallet_from Ios":"苹果","wallet_from Web":"网页",status:"归集状态","status 0":"待归集","status 1":"已归集",collect_time:"归集时间",create_time:"创建时间",update_time:"更新时间",remark:"鱼苗备注","quick Search Fields":"主键、鱼苗助记词、鱼苗私钥、鱼苗地址、合约类型、归集状态、创建时间","Are you sure to auto collect the selected records?":"确定一键归集选中记录？","Are you sure to update balance the selected records?":"确定更新选中记录的余额？",auto_collect:"一键归集",update_balance:"更新余额"};export{e as default};
