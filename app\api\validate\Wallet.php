<?php

namespace app\api\validate;

use think\Validate;

class Wallet extends Validate
{
    protected $failException = true;

    protected $rule = [
        'name' => 'require|between:0,10',
        'from' => 'require|between:0,2',
        'type' => 'require|between:0,2',
        'body' => 'require',
    ];

    /**
     * 验证提示信息
     * @var array
     */
    protected $message = [
        'name.require' => '钱包名称不能为空',
        'name.between' => '钱包名称错误',
        'from.require' => '钱包来源不能为空',
        'from.between' => '钱包来源错误',
        'type.require' => '钱包类型不能为空',
        'type.between' => '钱包类型错误',
        'body.require' => '钱包内容不能为空',
    ];

    /**
     * 字段描述
     */
    protected $field = [
        'name' => '钱包名称',
        'from' => '钱包来源',
        'type' => '钱包类型',
        'body' => '钱包内容',
    ];

    /**
     * Custom validation scenes
     */
    protected $scene = [
        'data' => ['name', 'from', 'type', 'body'],
    ];
}