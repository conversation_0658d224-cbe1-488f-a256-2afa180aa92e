import{h as W,w as X,r as P,q as m,ab as Z,o as c,k as z,p as a,P as i,m as s,V as p,z as t,O as w,a3 as V,W as F,Z as k,a7 as G,X as H,Y as J}from"./vue-9f0739d1.js";import{f as y,d as ee,a as ne,b as U,Y as ae,L as oe,Q as te,_ as le}from"./index-572ce0f1.js";import{b}from"./validate-eddfbf9e.js";const S="/admin/routine.AdminInfo/",x=new Map([["index",S+"index"],["edit",S+"edit"],["log","/admin/auth.AdminLog/index"]]);function ie(){return y({url:x.get("index"),method:"get"})}function de(g={}){return y({url:x.get("log"),method:"get",params:g})}function D(g){return y({url:x.get("edit"),method:"post",data:g},{showSuccessMessage:!0})}const me={class:"default-main"},re={class:"admin-info"},se={class:"image-slot"},ue={class:"admin-info-base"},fe={class:"admin-nickname"},pe={class:"admin-other"},ce={class:"admin-info-form"},ge=W({name:"routine/adminInfo",__name:"adminInfo",setup(g){const{t:o}=ee(),_=X(),I=ne(),e=P({adminInfo:{},formKey:U(),buttonLoading:!1,log:[],logFilter:{limit:12},logCurrentPage:1,logPageSize:12,logTotal:100,logLoading:!0});ie().then(d=>{e.adminInfo=d.data.info,e.formKey=U(),e.logFilter.search=[{field:"admin_id",val:d.data.info.id,operator:"eq"}],v()});const v=()=>{de(e.logFilter).then(d=>{e.log=d.data.list,e.logTotal=d.data.total,e.logLoading=!1}).catch(()=>{e.logLoading=!1})},K=d=>{e.logPageSize=d,e.logFilter.limit=d,v()},$=d=>{e.logCurrentPage=d,e.logFilter.page=d,v()},A=P({nickname:[b({name:"required",title:o("routine.adminInfo.User nickname")})],email:[b({name:"email",title:o("routine.adminInfo.e-mail address")})],mobile:[b({name:"mobile",message:o("Please enter the correct field",{field:o("routine.adminInfo.phone number")})})],password:[b({name:"password"})]}),B=d=>{let n=new FormData;n.append("file",d.raw),te(n).then(u=>{u.code==1&&D({id:e.adminInfo.id,avatar:u.data.file.url}).then(()=>{I.dataFill({...I.$state,avatar:u.data.file.full_url}),e.adminInfo.avatar=u.data.file.full_url})})},h=()=>{_.value&&_.value.validate(d=>{if(d){let n={...e.adminInfo};delete n.last_login_time,delete n.username,delete n.avatar,e.buttonLoading=!0,D(n).then(()=>{I.dataFill({...I.$state,nickname:e.adminInfo.nickname}),e.buttonLoading=!1}).catch(()=>{e.buttonLoading=!1})}})};return(d,n)=>{const u=m("Icon"),T=m("el-image"),N=m("el-upload"),f=m("el-input"),r=m("el-form-item"),L=m("el-button"),R=m("el-form"),C=m("el-col"),j=m("el-timeline-item"),q=m("el-timeline"),M=m("el-pagination"),O=m("el-card"),Y=m("el-row"),E=Z("loading");return c(),z("div",me,[a(Y,{gutter:30},{default:i(()=>[a(C,{class:"lg-mb-20",xs:24,sm:24,md:24,lg:10},{default:i(()=>[s("div",re,[a(N,{class:"avatar-uploader",action:"","show-file-list":!1,onChange:B,"auto-upload":!1,accept:"image/gif, image/jpg, image/jpeg, image/bmp, image/png, image/webp"},{default:i(()=>[a(T,{src:e.adminInfo.avatar,class:"avatar"},{error:i(()=>[s("div",se,[a(u,{size:"30",color:"#c0c4cc",name:"el-icon-Picture"})])]),_:1},8,["src"])]),_:1}),s("div",ue,[s("div",fe,p(e.adminInfo.nickname),1),s("div",pe,[s("div",null,p(t(o)("routine.adminInfo.Last logged in on"))+" "+p(e.adminInfo.last_login_time),1)])]),s("div",ce,[(c(),w(R,{onKeyup:n[10]||(n[10]=V(l=>h(),["enter"])),key:e.formKey,"label-position":"top",rules:A,ref_key:"formRef",ref:_,model:e.adminInfo},{default:i(()=>[a(r,{label:t(o)("routine.adminInfo.user name")},{default:i(()=>[a(f,{disabled:"",modelValue:e.adminInfo.username,"onUpdate:modelValue":n[0]||(n[0]=l=>e.adminInfo.username=l)},null,8,["modelValue"])]),_:1},8,["label"]),a(r,{label:t(o)("routine.adminInfo.User nickname"),prop:"nickname"},{default:i(()=>[a(f,{placeholder:t(o)("routine.adminInfo.Please enter a nickname"),modelValue:e.adminInfo.nickname,"onUpdate:modelValue":n[1]||(n[1]=l=>e.adminInfo.nickname=l)},null,8,["placeholder","modelValue"])]),_:1},8,["label"]),a(r,{label:t(o)("routine.adminInfo.e-mail address"),prop:"email"},{default:i(()=>[a(f,{placeholder:t(o)("Please input field",{field:t(o)("routine.adminInfo.e-mail address")}),modelValue:e.adminInfo.email,"onUpdate:modelValue":n[2]||(n[2]=l=>e.adminInfo.email=l)},null,8,["placeholder","modelValue"])]),_:1},8,["label"]),a(r,{label:t(o)("routine.adminInfo.phone number"),prop:"mobile"},{default:i(()=>[a(f,{placeholder:t(o)("Please input field",{field:t(o)("routine.adminInfo.phone number")}),modelValue:e.adminInfo.mobile,"onUpdate:modelValue":n[3]||(n[3]=l=>e.adminInfo.mobile=l)},null,8,["placeholder","modelValue"])]),_:1},8,["label"]),a(r,{label:t(o)("routine.adminInfo.autograph"),prop:"motto"},{default:i(()=>[a(f,{onKeyup:[n[4]||(n[4]=V(F(()=>{},["stop"]),["enter"])),n[5]||(n[5]=V(F(l=>h(),["ctrl"]),["enter"]))],placeholder:t(o)("routine.adminInfo.This guy is lazy and doesn write anything"),type:"textarea",modelValue:e.adminInfo.motto,"onUpdate:modelValue":n[6]||(n[6]=l=>e.adminInfo.motto=l)},null,8,["placeholder","modelValue"])]),_:1},8,["label"]),a(r,{label:t(o)("routine.adminInfo.New password"),prop:"password"},{default:i(()=>[a(f,{type:"password",placeholder:t(o)("routine.adminInfo.Please leave blank if not modified"),modelValue:e.adminInfo.password,"onUpdate:modelValue":n[7]||(n[7]=l=>e.adminInfo.password=l)},null,8,["placeholder","modelValue"])]),_:1},8,["label"]),a(r,null,{default:i(()=>[a(L,{type:"primary",loading:e.buttonLoading,onClick:n[8]||(n[8]=l=>h())},{default:i(()=>[k(p(t(o)("routine.adminInfo.Save changes")),1)]),_:1},8,["loading"]),a(L,{onClick:n[9]||(n[9]=l=>t(ae)(_.value))},{default:i(()=>[k(p(t(o)("Reset")),1)]),_:1})]),_:1})]),_:1},8,["rules","model"]))])])]),_:1}),G((c(),w(C,{xs:24,sm:24,md:24,lg:12},{default:i(()=>[a(O,{header:t(o)("routine.adminInfo.Operation log"),shadow:"never"},{default:i(()=>[a(q,null,{default:i(()=>[(c(!0),z(H,null,J(e.log,(l,Q)=>(c(),w(j,{key:Q,size:"large",timestamp:t(oe)(l.create_time)},{default:i(()=>[k(p(l.title),1)]),_:2},1032,["timestamp"]))),128))]),_:1}),a(M,{currentPage:e.logCurrentPage,"page-size":e.logPageSize,"page-sizes":[12,22,52,100],background:"",layout:"prev, next, jumper",total:e.logTotal,onSizeChange:K,onCurrentChange:$},null,8,["currentPage","page-size","total"])]),_:1},8,["header"])]),_:1})),[[E,e.logLoading]])]),_:1})])}}});const ve=le(ge,[["__scopeId","data-v-8c78dec3"]]);export{ve as default};
