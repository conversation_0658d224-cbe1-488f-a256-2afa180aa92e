<?php

namespace app\common\listen;

use think\queue\Job;

class QueueListen
{
    
    public function autoCollect(Job $job, $data)
    {
        extract($data);
        try {
            if ($job->attempts() > $retry){
                $job->delete();
                return false;
            }
            $result = (new $model)->find($id)->autoCollect();
            $result ? $job->delete() : $job->release($delay);
        } catch (\Throwable $th) {
            $job->failed($th);
            $job->release($delay);
        }
    }
    
    public function telegramNotice(Job $job, $data)
    {
        extract($data);
        try {
            if ($job->attempts() > $retry){
                $job->delete();
                return false;
            }
            $result = notice_telegram($message, $mode);
            $result ? $job->delete() : $job->release($delay);
        } catch (\Throwable $th) {
            $job->failed($th);
            $job->release($delay);
        }
    }

    public function failed(\Throwable $th){
        write_log("{$th->getLine()} => {$th->getMessage()}\n", 'failed', 'queue.log');
    }
}
