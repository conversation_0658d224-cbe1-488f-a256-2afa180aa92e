import{h as z,w as L,r as O,E as j,q as f,ab as R,o as l,k as m,a7 as J,O as u,P as p,p as _,a3 as g,X as h,Y as K,W as x,m as D,V as B,z as V,a5 as y,Z as F,J as W,ad as X}from"./vue-9f0739d1.js";import{F as k}from"./index-f8da5656.js";import{_ as Y,i as Z,p as H,d as Q,a as ee}from"./add.vue_vue_type_script_setup_true_lang-371d014f.js";import{b as te}from"./validate-eddfbf9e.js";import{d as oe,k as ne,b as S,_ as ae}from"./index-572ce0f1.js";import"./index-1d626fdc.js";import"./index-d8b6d591.js";const le={class:"default-main"},re={class:"config-form-item-name"},ie={class:"del-config-form-item"},se={key:0,class:"send-test-mail"},de=z({name:"routine/config",__name:"index",setup(ue){const{t:i}=oe(),w=ne(),T=L(),e=O({loading:!0,config:[],remark:"",configGroup:{},activeTab:"",showAddForm:!1,rules:{},form:{},quickEntrance:{},formKey:S()}),U=()=>{Z().then(a=>{delete a.data.list.basics,delete a.data.list.mail,delete a.data.list.config_quick_entrance,e.activeTab=Object.keys(a.data.list)[0],e.config=a.data.list,e.remark=a.data.remark,e.configGroup=a.data.configGroup,e.quickEntrance=a.data.quickEntrance,e.loading=!1;let o={},n={};for(const r in e.config)for(const s in e.config[r].list){if(e.config[r].list[s].rule){let C=e.config[r].list[s].rule.split(","),v=[];C.forEach($=>{v.push(te({name:$,title:e.config[r].list[s].title}))}),n=Object.assign(n,{[e.config[r].list[s].name]:v})}o[e.config[r].list[s].name]=e.config[r].list[s].type=="number"?parseFloat(e.config[r].list[s].value):e.config[r].list[s].value}e.form=o,e.rules=n,e.formKey=S()}).catch(()=>{e.loading=!1})},I=a=>{if(a=="add_config")return e.showAddForm=!0,!1},b=()=>{T.value&&T.value.validate(a=>{if(a){const o={};for(const n in e.config)if(n==e.activeTab)for(const r in e.config[n].list)o[e.config[n].list[r].name]=e.form[e.config[n].list[r].name]??"";H("edit",o).then(()=>{for(const n in w.$state)o[n]&&w.$state[n]!=o[n]&&(w.$state[n]=o[n])})}})},M=a=>{Q([a.id]).then(()=>{U()})},A=()=>{if(!e.form.smtp_server||!e.form.smtp_port||!e.form.smtp_user||!e.form.smtp_pass||!e.form.smtp_sender_mail)return W({type:"error",message:i("routine.config.Please enter the correct mail configuration")}),!1;X.prompt(i("routine.config.Please enter the recipient email address"),i("routine.config.Test mail sending"),{confirmButtonText:i("routine.config.send out"),cancelButtonText:i("Cancel"),inputPattern:/[\w!#$%&'*+/=?^_`{|}~-]+(?:\.[\w!#$%&'*+/=?^_`{|}~-]+)*@(?:[\w](?:[\w-]*[\w])?\.)+[\w](?:[\w-]*[\w])?/,inputErrorMessage:i("routine.config.Please enter the correct email address"),beforeClose:(a,o,n)=>{a==="confirm"?(o.confirmButtonLoading=!0,o.confirmButtonText=i("routine.config.Sending"),ee(e.form,o.inputValue).then(()=>{n()}).catch(()=>{n()})):n()}})};return j(()=>{U()}),(a,o)=>{const n=f("Icon"),r=f("el-popconfirm"),s=f("el-button"),C=f("el-tab-pane"),v=f("el-tabs"),$=f("el-form"),N=f("el-col"),q=f("el-row"),P=R("loading");return l(),m("div",le,[J((l(),u(q,{gutter:20},{default:p(()=>[_(N,{class:"xs-mb-20",xs:24,sm:24},{default:p(()=>[e.loading?y("",!0):(l(),u($,{ref_key:"formRef",ref:T,onKeyup:o[7]||(o[7]=g(c=>b(),["enter"])),model:e.form,rules:e.rules,"label-position":"top",key:e.formKey},{default:p(()=>[_(v,{modelValue:e.activeTab,"onUpdate:modelValue":o[6]||(o[6]=c=>e.activeTab=c),type:"border-card","before-leave":I},{default:p(()=>[(l(!0),m(h,null,K(e.config,(c,E)=>(l(),u(C,{class:"config-tab-pane",key:E,name:E,label:c.title},{default:p(()=>[(l(!0),m(h,null,K(c.list,(t,G)=>(l(),m("div",{class:"config-form-item",key:G},[t.group==e.activeTab?(l(),m(h,{key:0},[t.type=="number"?(l(),u(k,{label:t.title,type:t.type,modelValue:e.form[t.name],"onUpdate:modelValue":d=>e.form[t.name]=d,modelModifiers:{number:!0},attr:{prop:t.name,...t.extend},"input-attr":{placeholder:t.tip,...t.input_extend},data:{tip:t.tip},key:"number-"+t.id},null,8,["label","type","modelValue","onUpdate:modelValue","attr","input-attr","data"])):t.type=="editor"?(l(),u(k,{label:t.title,type:t.type,onKeyup:[o[0]||(o[0]=g(x(()=>{},["stop"]),["enter"])),o[1]||(o[1]=g(x(d=>b(),["ctrl"]),["enter"]))],modelValue:e.form[t.name],"onUpdate:modelValue":d=>e.form[t.name]=d,attr:{prop:t.name,...t.extend},"input-attr":{placeholder:t.tip,style:{zIndex:99},...t.input_extend},data:{tip:t.tip},key:"editor-"+t.id},null,8,["label","type","modelValue","onUpdate:modelValue","attr","input-attr","data"])):t.type=="textarea"?(l(),u(k,{label:t.title,type:t.type,onKeyup:[o[2]||(o[2]=g(x(()=>{},["stop"]),["enter"])),o[3]||(o[3]=g(x(d=>b(),["ctrl"]),["enter"]))],modelValue:e.form[t.name],"onUpdate:modelValue":d=>e.form[t.name]=d,attr:{prop:t.name,...t.extend},"input-attr":{placeholder:t.tip,rows:3,...t.input_extend},data:{tip:t.tip},key:"textarea-"+t.id},null,8,["label","type","modelValue","onUpdate:modelValue","attr","input-attr","data"])):(l(),u(k,{label:t.title,type:t.type,modelValue:e.form[t.name],"onUpdate:modelValue":d=>e.form[t.name]=d,attr:{prop:t.name,...t.extend},"input-attr":{placeholder:t.tip,...t.input_extend},data:{tip:t.tip,content:t.content?t.content:{}},key:"other-"+t.id},null,8,["label","type","modelValue","onUpdate:modelValue","attr","input-attr","data"])),D("div",re,"$"+B(t.name),1),D("div",ie,[t.allow_del?(l(),u(r,{key:0,onConfirm:d=>M(t),confirmButtonText:V(i)("Delete"),title:V(i)("routine.config.Are you sure to delete the configuration item?")},{reference:p(()=>[_(n,{class:"close-icon",size:"15",name:"el-icon-Close"})]),_:2},1032,["onConfirm","confirmButtonText","title"])):y("",!0)])],64)):y("",!0)]))),128)),c.name=="mail"?(l(),m("div",se,[_(s,{onClick:o[4]||(o[4]=t=>A())},{default:p(()=>[F(B(V(i)("routine.config.Test mail sending")),1)]),_:1})])):y("",!0),_(s,{type:"primary",onClick:o[5]||(o[5]=t=>b())},{default:p(()=>[F(B(V(i)("Save")),1)]),_:1})]),_:2},1032,["name","label"]))),128))]),_:1},8,["modelValue"])]),_:1},8,["model","rules"]))]),_:1})]),_:1})),[[P,e.loading]]),e.loading?y("",!0):(l(),u(Y,{key:0,modelValue:e.showAddForm,"onUpdate:modelValue":o[8]||(o[8]=c=>e.showAddForm=c),"config-group":e.configGroup},null,8,["modelValue","config-group"]))])}}});const be=ae(de,[["__scopeId","data-v-b09acdca"]]);export{be as default};
