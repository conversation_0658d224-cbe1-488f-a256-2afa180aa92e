const e={GroupName:"Group Name","Group name":"Group Name",jurisdiction:"Permissions","Parent group":"Superior group","The parent group cannot be the group itself":"The parent group cannot be the group itself","Manage subordinate role groups here":"In managing a subordinate role group (excluding a peer role group), you have all the rights of a subordinate role group and additional rights"};export{e as default};
