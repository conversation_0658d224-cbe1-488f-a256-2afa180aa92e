import w from"./popupForm-081e88a2.js";import{d as v,b as x,T as k,a as z}from"./index-1d626fdc.js";import{p as B}from"./index-d8b6d591.js";import{d as F,O as I,M as T,_ as q}from"./index-572ce0f1.js";import{h as E,w as P,ai as O,E as A,q as r,ab as D,o as i,k as K,m as u,z as t,O as m,a5 as _,p as l,P as c,a7 as L,V as M}from"./vue-9f0739d1.js";const R={class:"default-main"},C={class:"ba-table-box"},N={class:"mlr-12"},S={class:"table-header-operate-text"},G=E({name:"routine/attachment",__name:"index",setup(V){const{t:e}=F(),b=P(),f=v(["edit","delete"]);f[1].popconfirm.title=e("routine.attachment.Files and records will be deleted at the same time Are you sure?");const a=new x(new I("/admin/routine.Attachment/"),{column:[{type:"selection",align:"center",operator:!1},{label:e("Id"),prop:"id",align:"center",operator:"=",operatorPlaceholder:e("Id"),width:70},{label:e("utils.Breakdown"),prop:"topic",align:"center",operator:"LIKE",operatorPlaceholder:e("Fuzzy query")},{label:e("routine.attachment.Upload administrator"),prop:"admin.nickname",align:"center",operator:"LIKE",operatorPlaceholder:e("Fuzzy query")},{label:e("routine.attachment.Upload user"),prop:"user.nickname",align:"center",operator:"LIKE",operatorPlaceholder:e("Fuzzy query")},{label:e("utils.size"),prop:"size",align:"center",formatter:(n,p,d)=>{const s=parseFloat(d),o=Math.floor(Math.log(s)/Math.log(1024));return(s/Math.pow(1024,o)).toFixed(o<1?0:2)+" "+["B","KB","MB","GB","TB"][o]},operator:"RANGE",sortable:"custom",operatorPlaceholder:"bytes"},{label:e("utils.type"),prop:"mimetype",align:"center",operator:"LIKE",showOverflowTooltip:!0,operatorPlaceholder:e("Fuzzy query")},{label:e("utils.preview"),prop:"suffix",align:"center",renderFormatter:B,render:"image",operator:!1},{label:e("utils.Upload (Reference) times"),prop:"quote",align:"center",width:150,operator:"RANGE",sortable:"custom"},{label:e("utils.Original name"),prop:"name",align:"center",showOverflowTooltip:!0,operator:"LIKE",operatorPlaceholder:e("Fuzzy query")},{label:e("routine.attachment.Storage mode"),prop:"storage",align:"center",width:100,operator:"LIKE",operatorPlaceholder:e("Fuzzy query")},{label:e("utils.Last upload time"),prop:"last_upload_time",align:"center",render:"datetime",operator:"RANGE",width:160,sortable:"custom"},{label:e("Operate"),align:"center",width:"100",render:"buttons",buttons:f,operator:!1}],defaultOrder:{prop:"last_upload_time",order:"desc"}});return O("baTable",a),A(()=>{var n;a.table.ref=b.value,a.mount(),(n=a.getIndex())==null||n.then(()=>{a.initSort()})}),(n,p)=>{const d=r("el-alert"),s=r("Icon"),o=r("el-button"),h=r("el-tooltip"),y=r("el-popconfirm"),g=D("blur");return i(),K("div",R,[u("div",C,[t(a).table.remark?(i(),m(d,{key:0,class:"ba-table-alert",title:t(a).table.remark,type:"info","show-icon":""},null,8,["title"])):_("",!0),l(k,{buttons:["refresh","edit","comSearch","quickSearch","columnDisplay"],"quick-search-placeholder":t(e)("Quick search placeholder",{fields:t(e)("utils.Original name")})},{default:c(()=>[t(T)("del")?(i(),m(y,{key:0,onConfirm:p[0]||(p[0]=U=>t(a).onTableHeaderAction("delete",{})),"confirm-button-text":t(e)("Delete"),"cancel-button-text":t(e)("Cancel"),confirmButtonType:"danger",title:t(e)("routine.attachment.Files and records will be deleted at the same time Are you sure?"),disabled:!(t(a).table.selection.length>0)},{reference:c(()=>[u("div",N,[l(h,{content:t(e)("Delete selected row"),placement:"top"},{default:c(()=>[L((i(),m(o,{disabled:!(t(a).table.selection.length>0),class:"table-header-operate",type:"danger"},{default:c(()=>[l(s,{color:"#ffffff",name:"fa fa-trash"}),u("span",S,M(t(e)("Delete")),1)]),_:1},8,["disabled"])),[[g]])]),_:1},8,["content"])])]),_:1},8,["confirm-button-text","cancel-button-text","title","disabled"])):_("",!0)]),_:1},8,["quick-search-placeholder"]),l(z,{ref_key:"tableRef",ref:b},null,512),l(w)])])}}});const W=q(G,[["__scopeId","data-v-2a86eeff"]]);export{W as default};
