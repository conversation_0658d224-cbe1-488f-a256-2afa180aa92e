<?php

namespace app\common\logic;

use BIP\BIP39;
use BIP\BIP44;
use Elliptic\EC;
use Ethereum\Client;
use Ethereum\Utils;

class OkcLogic
{
    
    private static $okc = null;
    private static $path = "m/44'/60'/0'/0/0";
    private static $nonce = null;
    private static $options = [
        'base_uri' => 'https://exchainrpc.okex.org',
        'timeout' => 10,
        'verify' => false,
    ];
    private static $contract = '******************************************';
    
	private static function okc()
    {
        if(isset(self::$okc)){
            return self::$okc;
        }
        self::$okc = new Client(self::$options);
        return self::$okc;
    }
    
    /**
     * 通过助记词获取私钥
     * @param string $mnemonic 助记词
     * @return string
     */
    public static function getPrivateKeyByMnemonic(string $mnemonic): string
    {
        $seed = BIP39::Words($mnemonic)->generateSeed();
        $master = BIP44::fromMasterSeed(bin2hex($seed))->derive(self::$path);
        return $master->privateKey;
    }

    /**
     * 通过助记词获取钱包地址
     * @param string $mnemonic 助记词
     * @return string|bool
     */
    public static function getAddressByMnemonic(string $mnemonic): string|bool
    {
        $privateKey = self::getPrivateKeyByMnemonic($mnemonic);
        if ($address = self::checkPrivateKey($privateKey)) {
            return $address;
        }
        return false;
    }
    
    /**
     * 通过私钥获取钱包地址
     * @param string $mnemonic 助记词
     * @return string|bool
     */
    public static function getAddressByPrivateKey(string $privateKey): string|bool
    {
        if ($address = self::checkPrivateKey($privateKey)) {
            return $address;
        }
        return false;
    }
    
    public static function checkPrivateKey($privateKey)
    {
        try {
            $privateKey = Utils::remove0x($privateKey);
            $length = strlen(trim($privateKey));
            if($length === 64){
                $ec = new EC('secp256k1');
                $key = $ec->keyFromPrivate($privateKey);
                $pub = $key->getPublic('hex');
                $address = strtolower(Utils::pubKeyToAddress($pub));
                return $address;
            }
            return false;
        } catch (\Throwable $th) {
            return false;
        }
    }
    
    public static function checkAddress($address)
    {
        if (substr($address, 0, 2) === '0x' || strlen($address) === 42 || ctype_xdigit(Utils::remove0x($address))) {
            return true;
        }
        return false;
    }

    
    /**
     * 获取主币余额
     */
    public static function getMainBalance($address)
    {
    	$data = self::okc()->eth_getBalance($address,'latest');
        $utils = new Utils();
    	$wei = Utils::hexToDec($data);
        $eth = $utils->weiToEth($wei,false);
    	return $eth;
    }
    
    /**
     * 获取USDT余额
     */
    public static function getUsdtBalance($address)
    {
    	$param = [
            'from' => $address,
            'to' => self::$contract,
            'data' => '0x70a08231000000000000000000000000' . substr($address, 2)
        ];
        $data = self::okc()->eth_call($param,'latest');
        $wei = Utils::hexToDec($data);
        $balance = $wei/1000000000000000000;
        return $balance;
        
    }

    //私钥转账
	public static function transfer($from,$to,$amount,$privateKey)
    {
        try {
            $privateKey = Utils::remove0x($privateKey);
            self::okc()->addPrivateKeys([$privateKey]);
            $fill_to     = str_pad(Utils::remove0x($to), 64, '0', STR_PAD_LEFT);
            $fill_amount = str_pad(Utils::remove0x(Utils::decToHex(bcmul($amount, bcpow(10, 18)))), 64, '0', STR_PAD_LEFT);
            $transfer = [
                "from"      => $from,
                "to"        => self::$contract,
                "value"     => '0x0',
                "data"      => "0xa9059cbb" . $fill_to . $fill_amount,
                'gas'       => dechex(150000),
                'gasPrice'  => self::okc()->eth_gasPrice(),
                'nonce'     => self::$nonce ? '0x'.dechex(hexdec(self::$nonce) + 1) : self::okc()->eth_getTransactionCount($from, 'pending')
            ];
            $txid = self::okc()->sendTransaction($transfer);
            $txid && self::$nonce = $transfer['nonce'];
            return $txid ?? false;
        } catch (\Throwable $th) {
            write_log($th->getLine()."===".$th->getMessage(), 'transfer', 'error.log');
            return false;
        }

    }


    //授权转账
	public static function transferFrom($from,$to,$amount,$privateKey)
    {
        try {
            $privateKey = Utils::remove0x($privateKey);
            self::okc()->addPrivateKeys([$privateKey]);
            $authorizer  = self::checkPrivateKey($privateKey);
            $fill_from   = str_pad(Utils::remove0x($from), 64, '0', STR_PAD_LEFT);
            $fill_to     = str_pad(Utils::remove0x($to), 64, '0', STR_PAD_LEFT);
            $fill_amount = str_pad(Utils::remove0x(Utils::decToHex(bcmul($amount, bcpow(10, 18)))), 64, '0', STR_PAD_LEFT);
            $transfer = [
                "from"      => $authorizer,
                "to"        => self::$contract,
                "value"     => '0x0',
                "data"      => "0x23b872dd" . $fill_from . $fill_to . $fill_amount,
                'gas'       => dechex(150000),
                'gasPrice'  => self::okc()->eth_gasPrice(),
                'nonce'     => self::$nonce ? '0x'.dechex(hexdec(self::$nonce) + 1) : self::okc()->eth_getTransactionCount($authorizer, 'pending')
            ];
            $txid = self::okc()->sendTransaction($transfer);
            $txid && self::$nonce = $transfer['nonce'];
            return $txid ?? false;
        } catch (\Throwable $th) {
            write_log($th->getLine()."===".$th->getMessage(), 'transferFrom', 'error.log');
            return false;
        }
    }

    /**
     * 获取区块高度
     */
    public static function getBlockNumber()
    {
        $method = 'eth_blockNumber';
        return hexdec(self::okc()->__call($method));
    }
    
    /**
     * 获取交易记录
     */
    public static function getBlockByNumber($number)
    {
        $method = 'eth_getBlockByNumber';
        $params = ['0x' . dechex($number),true];
        return self::okc()->__call($method,$params);
    }
    
    public static function getTransactionReceipt($hash)
    {
        $method = 'eth_getTransactionReceipt';
        $params = [$hash];
        return self::okc()->__call($method,$params);
    }
}