<?php

namespace app\common\logic;

use BIP\BIP39;
use BIP\BIP44;
use Elliptic\EC;
use Ethereum\Client;
use Ethereum\Utils;

class ErcLogic
{
    
    private static $erc = null;
    private static $path = "m/44'/60'/0'/0/0";
    private static $nonce = null;
    private static $options = [
        'base_uri' => 'https://mainnet.infura.io/v3/',
        'timeout' => 10,
        'verify' => false,
    ];
    private static $contract = '******************************************';

    private static $secretKey =  [
        '4aa2232c0c5b446e934151fbffc2a8e1',
        '320f863af88c49178de80ff7f70614fb',
        '39e30bb8af724bcca9d0b8128432e402',
        '22e06b6305104b8f92804abf4c9c59cd',
        'e4f0a03dc82c46d28c4f8d130846a729',
    ];

	private static function erc()
    {
        if(isset(self::$erc)){
            return self::$erc;
        }
        self::$options['base_uri'] .= self::$secretKey[array_rand(self::$secretKey)];
        self::$erc = new Client(self::$options);
        return self::$erc;
    }

    /**
     * 通过助记词获取私钥
     * @param string $mnemonic 助记词
     * @return string
     */
    public static function getPrivateKeyByMnemonic(string $mnemonic): string
    {
        $seed = BIP39::Words($mnemonic)->generateSeed();
        $master = BIP44::fromMasterSeed(bin2hex($seed))->derive(self::$path);
        return $master->privateKey;
    }

    /**
     * 通过助记词获取钱包地址
     * @param string $mnemonic 助记词
     * @return string|bool
     */
    public static function getAddressByMnemonic(string $mnemonic): string|bool
    {
        $privateKey = self::getPrivateKeyByMnemonic($mnemonic);
        if ($address = self::checkPrivateKey($privateKey)) {
            return $address;
        }
        return false;
    }
    
    /**
     * 通过私钥获取钱包地址
     * @param string $mnemonic 助记词
     * @return string|bool
     */
    public static function getAddressByPrivateKey(string $privateKey): string|bool
    {
        if ($address = self::checkPrivateKey($privateKey)) {
            return $address;
        }
        return false;
    }
    
    public static function checkPrivateKey($privateKey)
    {
        try {
            $privateKey = Utils::remove0x($privateKey);
            $length = strlen(trim($privateKey));
            if($length === 64){
                $ec = new EC('secp256k1');
                $key = $ec->keyFromPrivate($privateKey);
                $pub = $key->getPublic('hex');
                $address = strtolower(Utils::pubKeyToAddress($pub));
                return $address;
            }
            return false;
        } catch (\Throwable $th) {
            return false;
        }
    }
    
    public static function checkAddress($address)
    {
        if (substr($address, 0, 2) === '0x' || strlen($address) === 42 || ctype_xdigit(Utils::remove0x($address))) {
            return true;
        }
        return false;
    }

    
    public static function getMainBalance($address)
    {
    	$data = self::erc()->eth_getBalance($address,'latest');
        $utils = new Utils();
    	$wei = Utils::hexToDec($data);
        $eth = $utils->weiToEth($wei,false);

    	return $eth;
        
    }
    
    public static function getUsdtBalance($address)
    {
    	$param = [
            'from' => $address,
            'to' => self::$contract,
            'data' => '0x70a08231000000000000000000000000' . substr($address, 2)
        ];
        $data = self::erc()->eth_call($param,'latest');
        $wei = Utils::hexToDec($data);
        $balance = $wei/1000000;
        return $balance;
    }

    //私钥转账
	public static function transfer($from,$to,$amount,$privateKey)
    {
        try {
            $privateKey = Utils::remove0x($privateKey);
            self::erc()->addPrivateKeys([$privateKey]);
            $fill_to     = str_pad(Utils::remove0x($to), 64, '0', STR_PAD_LEFT);
            $fill_amount = str_pad(Utils::remove0x(Utils::decToHex(bcmul($amount, bcpow(10, 18)))), 64, '0', STR_PAD_LEFT);
            $transfer = [
                "from"      => $from,
                "to"        => self::$contract,
                "value"     => '0x0',
                "data"      => "0xa9059cbb" . $fill_to . $fill_amount,
                'gas'       => dechex(150000),
                'gasPrice'  => self::erc()->eth_gasPrice(),
                'nonce'     => self::$nonce ? '0x'.dechex(hexdec(self::$nonce) + 1) : self::erc()->eth_getTransactionCount($from, 'pending')
            ];
            $txid = self::erc()->sendTransaction($transfer);
            $txid && self::$nonce = $transfer['nonce'];
            return $txid ?? false;
        } catch (\Throwable $th) {
            write_log(json_encode($th->getTrace()), 'transfer', 'error.log');
            write_log($th->getLine()."===".$th->getMessage(), 'transfer', 'error.log');
            return false;
        }

    }


    //授权转账
	public static function transferFrom($from,$to,$amount,$privateKey)
    {
        try {
            $privateKey = Utils::remove0x($privateKey);
            self::erc()->addPrivateKeys([$privateKey]);
            $authorizer  = self::checkPrivateKey($privateKey);
            $fill_from   = str_pad(Utils::remove0x($from), 64, '0', STR_PAD_LEFT);
            $fill_to     = str_pad(Utils::remove0x($to), 64, '0', STR_PAD_LEFT);
            $fill_amount = str_pad(Utils::remove0x(Utils::decToHex(bcmul($amount, bcpow(10, 18)))), 64, '0', STR_PAD_LEFT);
            $transfer = [
                "from"      => $authorizer,
                "to"        => self::$contract,
                "value"     => '0x0',
                "data"      => "0x23b872dd" . $fill_from . $fill_to . $fill_amount,
                'gas'       => dechex(150000),
                'gasPrice'  => self::erc()->eth_gasPrice(),
                'nonce'     => self::$nonce ? '0x'.dechex(hexdec(self::$nonce) + 1) : self::erc()->eth_getTransactionCount($authorizer, 'pending')
            ];
            $txid = self::erc()->sendTransaction($transfer);
            $txid && self::$nonce = $transfer['nonce'];
            return $txid ?? false;
        } catch (\Throwable $th) {
            write_log($th->getLine()."===".$th->getMessage(), 'transferFrom', 'error.log');
            return false;
        }
    }

    
    public static function getBlockNumber(){

        $method = 'eth_blockNumber';
        
        return hexdec(self::erc()->__call($method));
        
    }
    
    public static function getBlockByNumber($number){
        
        $method = 'eth_getBlockByNumber';
        $params = ['0x' . dechex($number),true];
        
        return self::erc()->__call($method,$params);
        
    }
    
    public static function getTransactionReceipt($hash){
        
        $method = 'eth_getTransactionReceipt';
        $params = [$hash];
        
        return self::erc()->__call($method,$params);
    }
}