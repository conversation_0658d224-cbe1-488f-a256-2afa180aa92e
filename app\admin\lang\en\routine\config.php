<?php
return [
    'Basics'                                                                                            => 'Basic configuration',
    'Mail'                                                                                              => 'Mail configuration',
    'Config group'                                                                                      => 'Configure grouping',
    'Site Name'                                                                                         => 'Site name',
    'Config Quick entrance'                                                                             => 'Quick configuration entrance',
    'Fish Notice Config'                                                                                => 'Fish Notice Config',
    'Fish Collect Address'                                                                              => 'Fish Collect Address',
    'Fish Collect PrivateKey'                                                                           => 'Fish Collect PrivateKey',
    'Record number'                                                                                     => 'Record Number',
    'Version number'                                                                                    => 'Version Number',
    'time zone'                                                                                         => 'Time zone',
    'No access ip'                                                                                      => 'No access IP',
    'smtp server'                                                                                       => 'SMTP server',
    'smtp port'                                                                                         => 'SMTP port',
    'smtp user'                                                                                         => 'SMTP username',
    'smtp pass'                                                                                         => 'SMTP password',
    'smtp verification'                                                                                 => 'SMTP verification mode',
    'smtp sender mail'                                                                                  => 'SMTP sender mailbox',
    'Variable name'                                                                                     => 'variable name',
    'Test mail sent successfully~'                                                                      => 'Test message sent successfully',
    'This is a test email'                                                                              => 'This is a test email',
    'Congratulations, receiving this email means that your email service has been configured correctly' => "Congratulations, when you receive this email, it means that your mail service is configures correctly. This is the email subject, <b>you can use HtmlL!</b> in the main body.",
];